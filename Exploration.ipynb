{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9766b337-83ac-49e0-8995-66547078c776", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 2, "id": "4f5c0c13-dc67-4b24-9ff8-01850790c395", "metadata": {}, "outputs": [], "source": ["df = pd.read_parquet('can_ocrevus_etl_output.parquet')"]}, {"cell_type": "code", "execution_count": 3, "id": "84e76a22-965a-420f-88d7-58f63e961646", "metadata": {}, "outputs": [{"data": {"text/plain": ["(12646, 35)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": 4, "id": "5d531e0a-0fca-44d3-849e-c825a3f43520", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patientid</th>\n", "      <th>currentphysicianid</th>\n", "      <th>patientprovince</th>\n", "      <th>drugtype</th>\n", "      <th>age</th>\n", "      <th>gender</th>\n", "      <th>lineoftherapy</th>\n", "      <th>patientstatus</th>\n", "      <th>patientsubstatus</th>\n", "      <th>diagnosis</th>\n", "      <th>...</th>\n", "      <th>copayflag</th>\n", "      <th>copaypercent</th>\n", "      <th>previous_ms_therapy_count</th>\n", "      <th>previous_ms_therapy_inf_count</th>\n", "      <th>therapyname_ms_off_label_count</th>\n", "      <th>ocr_insc_amt_covered</th>\n", "      <th>ocr_perc_insc_amt_covered</th>\n", "      <th>ocr_insc_amt_covered_12_mo</th>\n", "      <th>ocr_perc_insc_amt_covered_12_mo</th>\n", "      <th>prescribed_ocrevus_pats</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2181980</td>\n", "      <td>11694</td>\n", "      <td>SK</td>\n", "      <td>Commercial</td>\n", "      <td>35</td>\n", "      <td>M</td>\n", "      <td>0</td>\n", "      <td>Active in Program</td>\n", "      <td>OnTherapy</td>\n", "      <td>MS</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>1.0</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>314</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1819397</td>\n", "      <td>11575</td>\n", "      <td>QC</td>\n", "      <td>Commercial</td>\n", "      <td>39</td>\n", "      <td>F</td>\n", "      <td>3</td>\n", "      <td>Active in Program</td>\n", "      <td>OnTherapy</td>\n", "      <td>MS</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>37.0</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2205986</td>\n", "      <td>11679</td>\n", "      <td>QC</td>\n", "      <td>Commercial</td>\n", "      <td>42</td>\n", "      <td>F</td>\n", "      <td>2</td>\n", "      <td>Active in Program</td>\n", "      <td>Discontinued-Unknown</td>\n", "      <td>MS</td>\n", "      <td>...</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2375041</td>\n", "      <td>41521</td>\n", "      <td>ON</td>\n", "      <td>Commercial</td>\n", "      <td>53</td>\n", "      <td>F</td>\n", "      <td>0</td>\n", "      <td>Active in Program</td>\n", "      <td>OnTherapy</td>\n", "      <td>MS</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>152</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2404189</td>\n", "      <td>44728</td>\n", "      <td>AB</td>\n", "      <td>Commercial</td>\n", "      <td>29</td>\n", "      <td>F</td>\n", "      <td>0</td>\n", "      <td>Active in Program</td>\n", "      <td>OnTherapy</td>\n", "      <td>MS</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>36</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 35 columns</p>\n", "</div>"], "text/plain": ["   patientid  currentphysicianid patientprovince    drugtype  age gender  \\\n", "0    2181980               11694              SK  Commercial   35      M   \n", "1    1819397               11575              QC  Commercial   39      F   \n", "2    2205986               11679              QC  Commercial   42      F   \n", "3    2375041               41521              ON  Commercial   53      F   \n", "4    2404189               44728              AB  Commercial   29      F   \n", "\n", "   lineoftherapy      patientstatus      patientsubstatus diagnosis  ...  \\\n", "0              0  Active in Program             OnTherapy        MS  ...   \n", "1              3  Active in Program             OnTherapy        MS  ...   \n", "2              2  Active in Program  Discontinued-Unknown        MS  ...   \n", "3              0  Active in Program             OnTherapy        MS  ...   \n", "4              0  Active in Program             OnTherapy        MS  ...   \n", "\n", "  copayflag copaypercent previous_ms_therapy_count  \\\n", "0       Yes          1.0                      <NA>   \n", "1       Yes         37.0                         3   \n", "2      <NA>          NaN                         2   \n", "3        No          NaN                      <NA>   \n", "4       Yes          NaN                      <NA>   \n", "\n", "  previous_ms_therapy_inf_count  therapyname_ms_off_label_count  \\\n", "0                          <NA>                            <NA>   \n", "1                             1                               0   \n", "2                             0                               0   \n", "3                          <NA>                            <NA>   \n", "4                          <NA>                            <NA>   \n", "\n", "  ocr_insc_amt_covered  ocr_perc_insc_amt_covered  ocr_insc_amt_covered_12_mo  \\\n", "0                  NaN                        NaN                         NaN   \n", "1                  NaN                        NaN                         NaN   \n", "2                  NaN                        NaN                         NaN   \n", "3                  NaN                        NaN                         NaN   \n", "4                  NaN                        NaN                         NaN   \n", "\n", "  ocr_perc_insc_amt_covered_12_mo  prescribed_ocrevus_pats  \n", "0                             NaN                      314  \n", "1                             NaN                       38  \n", "2                             NaN                       30  \n", "3                             NaN                      152  \n", "4                             NaN                       36  \n", "\n", "[5 rows x 35 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 5, "id": "954ac6f7-6fdd-47be-9f4f-5c3c4f780031", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['patientid', 'currentphysicianid', 'patientprovince', 'drugtype', 'age',\n", "       'gender', 'lineoftherapy', 'patientstatus', 'patientsubstatus',\n", "       'diagnosis', 'programname', 'subdiagnosis', 'firstinfusiondate',\n", "       'coveragetype', 'discontinue_flag', 'latest_infusion_dt',\n", "       'total_infusions', 'financial_asst_active_flag', 'edss_test_value',\n", "       'edss_count', 'mri_count', 'edss_count_12_mo', 'mri_count_12_mo',\n", "       'insurertype', 'coveragestatus', 'copayflag', 'copaypercent',\n", "       'previous_ms_therapy_count', 'previous_ms_therapy_inf_count',\n", "       'therapyname_ms_off_label_count', 'ocr_insc_amt_covered',\n", "       'ocr_perc_insc_amt_covered', 'ocr_insc_amt_covered_12_mo',\n", "       'ocr_perc_insc_amt_covered_12_mo', 'prescribed_ocrevus_pats'],\n", "      dtype='object')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 6, "id": "a6c7958f-4f3a-4842-8764-294246554867", "metadata": {}, "outputs": [{"data": {"text/plain": ["drugtype\n", "Commercial    12634\n", "Bridging         12\n", "Name: count, dtype: Int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df['drugtype'].value_counts()"]}, {"cell_type": "markdown", "id": "9b4de859-2dd0-4f8d-be77-fec5dbadde5e", "metadata": {}, "source": ["#### What does \"Bridging' category in drug type mean? How is it going to be useful and do we need it in our final data?"]}, {"cell_type": "code", "execution_count": 7, "id": "0364598c-17d8-44d6-a5c2-602efa2b893b", "metadata": {}, "outputs": [{"data": {"text/plain": ["gender\n", "F    8286\n", "M    4360\n", "Name: count, dtype: Int64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df['gender'].value_counts()"]}, {"cell_type": "code", "execution_count": 8, "id": "298a850a-1dc9-44a3-9ec2-775d13986789", "metadata": {}, "outputs": [{"data": {"text/plain": ["gender\n", "F    0.655227\n", "M    0.344773\n", "Name: proportion, dtype: Float64"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df['gender'].value_counts(normalize=True)"]}, {"cell_type": "code", "execution_count": 9, "id": "23169c71-dcee-4461-94c4-e9f98a320e1e", "metadata": {}, "outputs": [{"data": {"text/plain": ["lineoftherapy\n", "0    6066\n", "1    3299\n", "2    1834\n", "3     859\n", "4     311\n", "5      99\n", "6      21\n", "7       2\n", "Name: count, dtype: Int64"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df['lineoftherapy'].value_counts()"]}, {"cell_type": "code", "execution_count": 10, "id": "e9d19ecd-d65c-4c9a-ab15-c61ea2d3a892", "metadata": {}, "outputs": [{"data": {"text/plain": ["patientstatus\n", "Active in Program    12646\n", "Name: count, dtype: Int64"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df['patientstatus'].value_counts()"]}, {"cell_type": "code", "execution_count": 11, "id": "3051f6b8-d475-4d50-b6ca-9d4354f79476", "metadata": {}, "outputs": [{"data": {"text/plain": ["patients<PERSON><PERSON>tus\n", "OnTherapy                                                           10325\n", "Discontinued-Unknown                                                  789\n", "Discontinued-MD Recommendation                                        541\n", "Discontinued-Switch to another therapy                                511\n", "Discontinued-Personal                                                 206\n", "Discontinued-Patient Experienced Adverse Event with Program Drug      141\n", "Discontinued-Lost to F/U                                               47\n", "Discontinued-Program Drug Lack of Efficacy                             37\n", "Discontinued-Disease Progression                                       30\n", "Discontinued-Financial Concern                                         10\n", "Discontinued-Other                                                      3\n", "Pending-Clinic - Pending Infusion Appointment                           2\n", "Pending-Patient - Pending Enrollment                                    2\n", "Pending-Infusion Scheduled                                              1\n", "Pending-HCP - Pending Infusion Readiness                                1\n", "Name: count, dtype: Int64"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df['patientsubstatus'].value_counts()"]}, {"cell_type": "markdown", "id": "edc74428-c6b2-40dd-90ac-373f3a02d80b", "metadata": {}, "source": ["#### We need only patients who are currently on ocrevus therapy and who were discontinued from the ocrevus therapy? Do we need to have data about patients whose therapy is in pending status?"]}, {"cell_type": "code", "execution_count": 12, "id": "fda92bf6-d4d8-45de-86cc-c3bc3861af3c", "metadata": {}, "outputs": [{"data": {"text/plain": ["diagnosis\n", "MS    12646\n", "Name: count, dtype: Int64"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["df['diagnosis'].value_counts()"]}, {"cell_type": "code", "execution_count": 13, "id": "b3117a64-7c8b-4d9e-9ed0-8c076d7a76a4", "metadata": {}, "outputs": [{"data": {"text/plain": ["programname\n", "Ocrevus    12646\n", "Name: count, dtype: Int64"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df['programname'].value_counts()"]}, {"cell_type": "code", "execution_count": 14, "id": "6cde34f7-4df3-4832-9342-134459c5af77", "metadata": {}, "outputs": [{"data": {"text/plain": ["subdiagnosis\n", "RRMS    10485\n", "PPMS     2146\n", "SPMS       15\n", "Name: count, dtype: Int64"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["df['subdiagnosis'].value_counts()"]}, {"cell_type": "code", "execution_count": 15, "id": "a71da6fb-91dc-48de-8338-f4541df0f310", "metadata": {}, "outputs": [{"data": {"text/plain": ["coveragetype\n", "Private    4538\n", "Public     4020\n", "Combo      2130\n", "None       1958\n", "Name: count, dtype: Int64"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["df['coveragetype'].value_counts()"]}, {"cell_type": "code", "execution_count": 16, "id": "00ae5af8-28fc-46a8-be2f-998fb9dbd767", "metadata": {}, "outputs": [], "source": ["pd.set_option('display.max_columns', None)"]}, {"cell_type": "code", "execution_count": 17, "id": "273e1eb5-6211-4488-aaf7-2aff86e1d070", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patientid</th>\n", "      <th>currentphysicianid</th>\n", "      <th>patientprovince</th>\n", "      <th>drugtype</th>\n", "      <th>age</th>\n", "      <th>gender</th>\n", "      <th>lineoftherapy</th>\n", "      <th>patientstatus</th>\n", "      <th>patientsubstatus</th>\n", "      <th>diagnosis</th>\n", "      <th>programname</th>\n", "      <th>subdiagnosis</th>\n", "      <th>firstinfusiondate</th>\n", "      <th>coveragetype</th>\n", "      <th>discontinue_flag</th>\n", "      <th>latest_infusion_dt</th>\n", "      <th>total_infusions</th>\n", "      <th>financial_asst_active_flag</th>\n", "      <th>edss_test_value</th>\n", "      <th>edss_count</th>\n", "      <th>mri_count</th>\n", "      <th>edss_count_12_mo</th>\n", "      <th>mri_count_12_mo</th>\n", "      <th>insurertype</th>\n", "      <th>coveragestatus</th>\n", "      <th>copayflag</th>\n", "      <th>copaypercent</th>\n", "      <th>previous_ms_therapy_count</th>\n", "      <th>previous_ms_therapy_inf_count</th>\n", "      <th>therapyname_ms_off_label_count</th>\n", "      <th>ocr_insc_amt_covered</th>\n", "      <th>ocr_perc_insc_amt_covered</th>\n", "      <th>ocr_insc_amt_covered_12_mo</th>\n", "      <th>ocr_perc_insc_amt_covered_12_mo</th>\n", "      <th>prescribed_ocrevus_pats</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2181980</td>\n", "      <td>11694</td>\n", "      <td>SK</td>\n", "      <td>Commercial</td>\n", "      <td>35</td>\n", "      <td>M</td>\n", "      <td>0</td>\n", "      <td>Active in Program</td>\n", "      <td>OnTherapy</td>\n", "      <td>MS</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>RRMS</td>\n", "      <td>2023-08-09</td>\n", "      <td>Public</td>\n", "      <td>0</td>\n", "      <td>2025-02-05</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>Not Provided</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Public</td>\n", "      <td>SA Required</td>\n", "      <td>Yes</td>\n", "      <td>1.0</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>314</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1819397</td>\n", "      <td>11575</td>\n", "      <td>QC</td>\n", "      <td>Commercial</td>\n", "      <td>39</td>\n", "      <td>F</td>\n", "      <td>3</td>\n", "      <td>Active in Program</td>\n", "      <td>OnTherapy</td>\n", "      <td>MS</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>RRMS</td>\n", "      <td>2019-09-24</td>\n", "      <td>Public</td>\n", "      <td>0</td>\n", "      <td>2025-02-27</td>\n", "      <td>12</td>\n", "      <td>0</td>\n", "      <td>6.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Public</td>\n", "      <td>Covered</td>\n", "      <td>Yes</td>\n", "      <td>37.0</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2205986</td>\n", "      <td>11679</td>\n", "      <td>QC</td>\n", "      <td>Commercial</td>\n", "      <td>42</td>\n", "      <td>F</td>\n", "      <td>2</td>\n", "      <td>Active in Program</td>\n", "      <td>Discontinued-Unknown</td>\n", "      <td>MS</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>RRMS</td>\n", "      <td>2022-07-11</td>\n", "      <td>None</td>\n", "      <td>1</td>\n", "      <td>2022-07-11</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Not Provided</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2375041</td>\n", "      <td>41521</td>\n", "      <td>ON</td>\n", "      <td>Commercial</td>\n", "      <td>53</td>\n", "      <td>F</td>\n", "      <td>0</td>\n", "      <td>Active in Program</td>\n", "      <td>OnTherapy</td>\n", "      <td>MS</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>PPMS</td>\n", "      <td>2024-04-25</td>\n", "      <td>Private</td>\n", "      <td>0</td>\n", "      <td>2025-05-09</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>Not Provided</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Private</td>\n", "      <td>Covered</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>152</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2404189</td>\n", "      <td>44728</td>\n", "      <td>AB</td>\n", "      <td>Commercial</td>\n", "      <td>29</td>\n", "      <td>F</td>\n", "      <td>0</td>\n", "      <td>Active in Program</td>\n", "      <td>OnTherapy</td>\n", "      <td>MS</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>RRMS</td>\n", "      <td>2024-04-23</td>\n", "      <td>Public</td>\n", "      <td>0</td>\n", "      <td>2025-04-21</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>1.5</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Public</td>\n", "      <td>SA Required</td>\n", "      <td>Yes</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>36</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   patientid  currentphysicianid patientprovince    drugtype  age gender  \\\n", "0    2181980               11694              SK  Commercial   35      M   \n", "1    1819397               11575              QC  Commercial   39      F   \n", "2    2205986               11679              QC  Commercial   42      F   \n", "3    2375041               41521              ON  Commercial   53      F   \n", "4    2404189               44728              AB  Commercial   29      F   \n", "\n", "   lineoftherapy      patientstatus      patientsubstatus diagnosis  \\\n", "0              0  Active in Program             OnTherapy        MS   \n", "1              3  Active in Program             OnTherapy        MS   \n", "2              2  Active in Program  Discontinued-Unknown        MS   \n", "3              0  Active in Program             OnTherapy        MS   \n", "4              0  Active in Program             OnTherapy        MS   \n", "\n", "  programname subdiagnosis firstinfusiondate coveragetype  discontinue_flag  \\\n", "0     Ocrevus         RRMS        2023-08-09       Public                 0   \n", "1     Ocrevus         RRMS        2019-09-24       Public                 0   \n", "2     Ocrevus         RRMS        2022-07-11         None                 1   \n", "3     Ocrevus         PPMS        2024-04-25      Private                 0   \n", "4     Ocrevus         RRMS        2024-04-23       Public                 0   \n", "\n", "  latest_infusion_dt  total_infusions  financial_asst_active_flag  \\\n", "0         2025-02-05                5                           0   \n", "1         2025-02-27               12                           0   \n", "2         2022-07-11                1                           0   \n", "3         2025-05-09                4                           0   \n", "4         2025-04-21                4                           1   \n", "\n", "  edss_test_value  edss_count  mri_count  edss_count_12_mo  mri_count_12_mo  \\\n", "0    Not Provided           1          1                 0                0   \n", "1             6.0           1          0                 0                0   \n", "2    Not Provided           1          0                 0                0   \n", "3    Not Provided           1          0                 0                0   \n", "4             1.5           1          0                 0                0   \n", "\n", "  insurertype coveragestatus copayflag  copaypercent  \\\n", "0      Public    SA Required       Yes           1.0   \n", "1      Public        Covered       Yes          37.0   \n", "2        <NA>           <NA>      <NA>           NaN   \n", "3     Private        Covered        No           NaN   \n", "4      Public    SA Required       Yes           NaN   \n", "\n", "   previous_ms_therapy_count  previous_ms_therapy_inf_count  \\\n", "0                       <NA>                           <NA>   \n", "1                          3                              1   \n", "2                          2                              0   \n", "3                       <NA>                           <NA>   \n", "4                       <NA>                           <NA>   \n", "\n", "   therapyname_ms_off_label_count  ocr_insc_amt_covered  \\\n", "0                            <NA>                   NaN   \n", "1                               0                   NaN   \n", "2                               0                   NaN   \n", "3                            <NA>                   NaN   \n", "4                            <NA>                   NaN   \n", "\n", "   ocr_perc_insc_amt_covered  ocr_insc_amt_covered_12_mo  \\\n", "0                        NaN                         NaN   \n", "1                        NaN                         NaN   \n", "2                        NaN                         NaN   \n", "3                        NaN                         NaN   \n", "4                        NaN                         NaN   \n", "\n", "   ocr_perc_insc_amt_covered_12_mo  prescribed_ocrevus_pats  \n", "0                              NaN                      314  \n", "1                              NaN                       38  \n", "2                              NaN                       30  \n", "3                              NaN                      152  \n", "4                              NaN                       36  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 18, "id": "8ab7bb22-a7e6-4978-850a-4f146bf342f8", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['patientid', 'currentphysicianid', 'patientprovince', 'drugtype', 'age',\n", "       'gender', 'lineoftherapy', 'patientstatus', 'patientsubstatus',\n", "       'diagnosis', 'programname', 'subdiagnosis', 'firstinfusiondate',\n", "       'coveragetype', 'discontinue_flag', 'latest_infusion_dt',\n", "       'total_infusions', 'financial_asst_active_flag', 'edss_test_value',\n", "       'edss_count', 'mri_count', 'edss_count_12_mo', 'mri_count_12_mo',\n", "       'insurertype', 'coveragestatus', 'copayflag', 'copaypercent',\n", "       'previous_ms_therapy_count', 'previous_ms_therapy_inf_count',\n", "       'therapyname_ms_off_label_count', 'ocr_insc_amt_covered',\n", "       'ocr_perc_insc_amt_covered', 'ocr_insc_amt_covered_12_mo',\n", "       'ocr_perc_insc_amt_covered_12_mo', 'prescribed_ocrevus_pats'],\n", "      dtype='object')"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 19, "id": "01ea1db6-3442-4542-8c50-ab4fff4545a7", "metadata": {}, "outputs": [{"data": {"text/plain": ["insurertype\n", "Private    5371\n", "Public     5229\n", "<NA>       1904\n", "Federal     136\n", "Cash          6\n", "Name: count, dtype: Int64"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["df['insurertype'].value_counts(dropna=False)"]}, {"cell_type": "markdown", "id": "7605d345-a2b9-4f8f-bf91-4b96c8974564", "metadata": {}, "source": ["#### What is the difference between coveragetype and insurertype features. Do we need to have both the features?"]}, {"cell_type": "code", "execution_count": 20, "id": "8f6e6c4e-be94-40e7-8c0e-f872d5b26653", "metadata": {}, "outputs": [{"data": {"text/plain": ["coveragestatus\n", "SA Required                   8912\n", "<NA>                          1905\n", "Covered                       1609\n", "Not Covered                    165\n", "Investigation Discontinued      25\n", "Pending                         14\n", "Reimbursement On Hold           11\n", "Investigation by pharmacy        5\n", "Name: count, dtype: Int64"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["df['coveragestatus'].value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": 21, "id": "3418f75a-e42c-4887-b59c-b7c356019ca1", "metadata": {}, "outputs": [{"data": {"text/plain": ["copayflag\n", "Yes       5438\n", "No        3527\n", "<NA>      2181\n", "Unsure    1500\n", "Name: count, dtype: Int64"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["df['copayflag'].value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": 22, "id": "30612983-86c9-4f67-a86f-f1fac6ba115e", "metadata": {}, "outputs": [{"data": {"text/plain": ["8495"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["df['copaypercent'].isna().sum()"]}, {"cell_type": "code", "execution_count": 23, "id": "d960f1ce-f513-494e-bb47-ce2283caa015", "metadata": {}, "outputs": [{"data": {"text/plain": ["6051"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["df['previous_ms_therapy_count'].isna().sum()"]}, {"cell_type": "code", "execution_count": 24, "id": "113463fb-a75a-4fbc-8600-665b4b1e9e0a", "metadata": {}, "outputs": [{"data": {"text/plain": ["6051"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["df['previous_ms_therapy_inf_count'].isna().sum()"]}, {"cell_type": "markdown", "id": "1629fa58-f311-4780-a9c8-ff28cf990737", "metadata": {}, "source": ["#### What is the difference between previous_ms_therapy_count and previous_ms_therapy_inf_count features. They have same no of null values"]}, {"cell_type": "code", "execution_count": 25, "id": "965eca48-954d-44f7-9b21-b4c6fb4a969d", "metadata": {}, "outputs": [{"data": {"text/plain": ["edss_test_value\n", "2.0        1809\n", "1.0        1281\n", "3.0        1144\n", "2.5        1032\n", "1.5        1010\n", "           ... \n", "4.5?          1\n", "unkown        1\n", "3.0-3.5       1\n", ".5            1\n", "0 - 1         1\n", "Name: count, Length: 81, dtype: Int64"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["df['edss_test_value'].value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": 26, "id": "474385bc-a188-4175-85fc-cf967d79572c", "metadata": {}, "outputs": [{"data": {"text/plain": ["314"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["df['edss_test_value'].isna().sum()"]}, {"cell_type": "code", "execution_count": 27, "id": "fc8ffd6c-95fb-4bd8-9c78-e0d6f539feea", "metadata": {}, "outputs": [{"data": {"text/plain": ["<IntegerArray>\n", "[1, 2, 3, 0, <NA>, 4, 5]\n", "Length: 7, dtype: Int64"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["df['edss_count'].unique()"]}, {"cell_type": "code", "execution_count": 28, "id": "bf4cf81f-8bb2-4af3-abf1-7186903b74db", "metadata": {}, "outputs": [{"data": {"text/plain": ["patientid                              0\n", "currentphysicianid                     0\n", "patientprovince                        0\n", "drugtype                               0\n", "age                                    0\n", "gender                                 0\n", "lineoftherapy                        155\n", "patientstatus                          0\n", "patientsubstatus                       0\n", "diagnosis                              0\n", "programname                            0\n", "subdiagnosis                           0\n", "firstinfusiondate                      6\n", "coveragetype                           0\n", "discontinue_flag                       0\n", "latest_infusion_dt                     0\n", "total_infusions                        8\n", "financial_asst_active_flag             0\n", "edss_test_value                      314\n", "edss_count                           224\n", "mri_count                            224\n", "edss_count_12_mo                     224\n", "mri_count_12_mo                      224\n", "insurertype                         1904\n", "coveragestatus                      1905\n", "copayflag                           2181\n", "copaypercent                        8495\n", "previous_ms_therapy_count           6051\n", "previous_ms_therapy_inf_count       6051\n", "therapyname_ms_off_label_count      6051\n", "ocr_insc_amt_covered               12434\n", "ocr_perc_insc_amt_covered          12434\n", "ocr_insc_amt_covered_12_mo         12434\n", "ocr_perc_insc_amt_covered_12_mo    12434\n", "prescribed_ocrevus_pats                1\n", "dtype: int64"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["df.isna().sum()"]}, {"cell_type": "code", "execution_count": 29, "id": "5d3a81a7-9844-42a3-aad2-d691c3d14c79", "metadata": {}, "outputs": [{"data": {"text/plain": ["patientid                           0.000000\n", "currentphysicianid                  0.000000\n", "patientprovince                     0.000000\n", "drugtype                            0.000000\n", "age                                 0.000000\n", "gender                              0.000000\n", "lineoftherapy                       1.225684\n", "patientstatus                       0.000000\n", "patientsubstatus                    0.000000\n", "diagnosis                           0.000000\n", "programname                         0.000000\n", "subdiagnosis                        0.000000\n", "firstinfusiondate                   0.047446\n", "coveragetype                        0.000000\n", "discontinue_flag                    0.000000\n", "latest_infusion_dt                  0.000000\n", "total_infusions                     0.063261\n", "financial_asst_active_flag          0.000000\n", "edss_test_value                     2.482999\n", "edss_count                          1.771311\n", "mri_count                           1.771311\n", "edss_count_12_mo                    1.771311\n", "mri_count_12_mo                     1.771311\n", "insurertype                        15.056144\n", "coveragestatus                     15.064052\n", "copayflag                          17.246560\n", "copaypercent                       67.175391\n", "previous_ms_therapy_count          47.849122\n", "previous_ms_therapy_inf_count      47.849122\n", "therapyname_ms_off_label_count     47.849122\n", "ocr_insc_amt_covered               98.323581\n", "ocr_perc_insc_amt_covered          98.323581\n", "ocr_insc_amt_covered_12_mo         98.323581\n", "ocr_perc_insc_amt_covered_12_mo    98.323581\n", "prescribed_ocrevus_pats             0.007908\n", "dtype: float64"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["df.isna().mean() * 100"]}, {"cell_type": "code", "execution_count": 30, "id": "ec420690-865e-45aa-8d0d-b9308c274b3c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patientid</th>\n", "      <th>currentphysicianid</th>\n", "      <th>patientprovince</th>\n", "      <th>drugtype</th>\n", "      <th>age</th>\n", "      <th>gender</th>\n", "      <th>lineoftherapy</th>\n", "      <th>patientstatus</th>\n", "      <th>patientsubstatus</th>\n", "      <th>diagnosis</th>\n", "      <th>programname</th>\n", "      <th>subdiagnosis</th>\n", "      <th>firstinfusiondate</th>\n", "      <th>coveragetype</th>\n", "      <th>discontinue_flag</th>\n", "      <th>latest_infusion_dt</th>\n", "      <th>total_infusions</th>\n", "      <th>financial_asst_active_flag</th>\n", "      <th>edss_test_value</th>\n", "      <th>edss_count</th>\n", "      <th>mri_count</th>\n", "      <th>edss_count_12_mo</th>\n", "      <th>mri_count_12_mo</th>\n", "      <th>insurertype</th>\n", "      <th>coveragestatus</th>\n", "      <th>copayflag</th>\n", "      <th>copaypercent</th>\n", "      <th>previous_ms_therapy_count</th>\n", "      <th>previous_ms_therapy_inf_count</th>\n", "      <th>therapyname_ms_off_label_count</th>\n", "      <th>ocr_insc_amt_covered</th>\n", "      <th>ocr_perc_insc_amt_covered</th>\n", "      <th>ocr_insc_amt_covered_12_mo</th>\n", "      <th>ocr_perc_insc_amt_covered_12_mo</th>\n", "      <th>prescribed_ocrevus_pats</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2181980</td>\n", "      <td>11694</td>\n", "      <td>SK</td>\n", "      <td>Commercial</td>\n", "      <td>35</td>\n", "      <td>M</td>\n", "      <td>0</td>\n", "      <td>Active in Program</td>\n", "      <td>OnTherapy</td>\n", "      <td>MS</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>RRMS</td>\n", "      <td>2023-08-09</td>\n", "      <td>Public</td>\n", "      <td>0</td>\n", "      <td>2025-02-05</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>Not Provided</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Public</td>\n", "      <td>SA Required</td>\n", "      <td>Yes</td>\n", "      <td>1.0</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>314</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1819397</td>\n", "      <td>11575</td>\n", "      <td>QC</td>\n", "      <td>Commercial</td>\n", "      <td>39</td>\n", "      <td>F</td>\n", "      <td>3</td>\n", "      <td>Active in Program</td>\n", "      <td>OnTherapy</td>\n", "      <td>MS</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>RRMS</td>\n", "      <td>2019-09-24</td>\n", "      <td>Public</td>\n", "      <td>0</td>\n", "      <td>2025-02-27</td>\n", "      <td>12</td>\n", "      <td>0</td>\n", "      <td>6.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Public</td>\n", "      <td>Covered</td>\n", "      <td>Yes</td>\n", "      <td>37.0</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2205986</td>\n", "      <td>11679</td>\n", "      <td>QC</td>\n", "      <td>Commercial</td>\n", "      <td>42</td>\n", "      <td>F</td>\n", "      <td>2</td>\n", "      <td>Active in Program</td>\n", "      <td>Discontinued-Unknown</td>\n", "      <td>MS</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>RRMS</td>\n", "      <td>2022-07-11</td>\n", "      <td>None</td>\n", "      <td>1</td>\n", "      <td>2022-07-11</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Not Provided</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2375041</td>\n", "      <td>41521</td>\n", "      <td>ON</td>\n", "      <td>Commercial</td>\n", "      <td>53</td>\n", "      <td>F</td>\n", "      <td>0</td>\n", "      <td>Active in Program</td>\n", "      <td>OnTherapy</td>\n", "      <td>MS</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>PPMS</td>\n", "      <td>2024-04-25</td>\n", "      <td>Private</td>\n", "      <td>0</td>\n", "      <td>2025-05-09</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>Not Provided</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Private</td>\n", "      <td>Covered</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>152</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2404189</td>\n", "      <td>44728</td>\n", "      <td>AB</td>\n", "      <td>Commercial</td>\n", "      <td>29</td>\n", "      <td>F</td>\n", "      <td>0</td>\n", "      <td>Active in Program</td>\n", "      <td>OnTherapy</td>\n", "      <td>MS</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>RRMS</td>\n", "      <td>2024-04-23</td>\n", "      <td>Public</td>\n", "      <td>0</td>\n", "      <td>2025-04-21</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>1.5</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Public</td>\n", "      <td>SA Required</td>\n", "      <td>Yes</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>36</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   patientid  currentphysicianid patientprovince    drugtype  age gender  \\\n", "0    2181980               11694              SK  Commercial   35      M   \n", "1    1819397               11575              QC  Commercial   39      F   \n", "2    2205986               11679              QC  Commercial   42      F   \n", "3    2375041               41521              ON  Commercial   53      F   \n", "4    2404189               44728              AB  Commercial   29      F   \n", "\n", "   lineoftherapy      patientstatus      patientsubstatus diagnosis  \\\n", "0              0  Active in Program             OnTherapy        MS   \n", "1              3  Active in Program             OnTherapy        MS   \n", "2              2  Active in Program  Discontinued-Unknown        MS   \n", "3              0  Active in Program             OnTherapy        MS   \n", "4              0  Active in Program             OnTherapy        MS   \n", "\n", "  programname subdiagnosis firstinfusiondate coveragetype  discontinue_flag  \\\n", "0     Ocrevus         RRMS        2023-08-09       Public                 0   \n", "1     Ocrevus         RRMS        2019-09-24       Public                 0   \n", "2     Ocrevus         RRMS        2022-07-11         None                 1   \n", "3     Ocrevus         PPMS        2024-04-25      Private                 0   \n", "4     Ocrevus         RRMS        2024-04-23       Public                 0   \n", "\n", "  latest_infusion_dt  total_infusions  financial_asst_active_flag  \\\n", "0         2025-02-05                5                           0   \n", "1         2025-02-27               12                           0   \n", "2         2022-07-11                1                           0   \n", "3         2025-05-09                4                           0   \n", "4         2025-04-21                4                           1   \n", "\n", "  edss_test_value  edss_count  mri_count  edss_count_12_mo  mri_count_12_mo  \\\n", "0    Not Provided           1          1                 0                0   \n", "1             6.0           1          0                 0                0   \n", "2    Not Provided           1          0                 0                0   \n", "3    Not Provided           1          0                 0                0   \n", "4             1.5           1          0                 0                0   \n", "\n", "  insurertype coveragestatus copayflag  copaypercent  \\\n", "0      Public    SA Required       Yes           1.0   \n", "1      Public        Covered       Yes          37.0   \n", "2        <NA>           <NA>      <NA>           NaN   \n", "3     Private        Covered        No           NaN   \n", "4      Public    SA Required       Yes           NaN   \n", "\n", "   previous_ms_therapy_count  previous_ms_therapy_inf_count  \\\n", "0                       <NA>                           <NA>   \n", "1                          3                              1   \n", "2                          2                              0   \n", "3                       <NA>                           <NA>   \n", "4                       <NA>                           <NA>   \n", "\n", "   therapyname_ms_off_label_count  ocr_insc_amt_covered  \\\n", "0                            <NA>                   NaN   \n", "1                               0                   NaN   \n", "2                               0                   NaN   \n", "3                            <NA>                   NaN   \n", "4                            <NA>                   NaN   \n", "\n", "   ocr_perc_insc_amt_covered  ocr_insc_amt_covered_12_mo  \\\n", "0                        NaN                         NaN   \n", "1                        NaN                         NaN   \n", "2                        NaN                         NaN   \n", "3                        NaN                         NaN   \n", "4                        NaN                         NaN   \n", "\n", "   ocr_perc_insc_amt_covered_12_mo  prescribed_ocrevus_pats  \n", "0                              NaN                      314  \n", "1                              NaN                       38  \n", "2                              NaN                       30  \n", "3                              NaN                      152  \n", "4                              NaN                       36  "]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 31, "id": "d509b979-5071-403b-9662-1f38f9ae8012", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['patientid', 'currentphysicianid', 'patientprovince', 'drugtype', 'age',\n", "       'gender', 'lineoftherapy', 'patientstatus', 'patientsubstatus',\n", "       'diagnosis', 'programname', 'subdiagnosis', 'firstinfusiondate',\n", "       'coveragetype', 'discontinue_flag', 'latest_infusion_dt',\n", "       'total_infusions', 'financial_asst_active_flag', 'edss_test_value',\n", "       'edss_count', 'mri_count', 'edss_count_12_mo', 'mri_count_12_mo',\n", "       'insurertype', 'coveragestatus', 'copayflag', 'copaypercent',\n", "       'previous_ms_therapy_count', 'previous_ms_therapy_inf_count',\n", "       'therapyname_ms_off_label_count', 'ocr_insc_amt_covered',\n", "       'ocr_perc_insc_amt_covered', 'ocr_insc_amt_covered_12_mo',\n", "       'ocr_perc_insc_amt_covered_12_mo', 'prescribed_ocrevus_pats'],\n", "      dtype='object')"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "markdown", "id": "34799a9f-3173-4a09-8a83-bf176771f94c", "metadata": {}, "source": ["#### Have a few questions about the features in the data, what do these features mean and how were they created\n", "\n", "- I can see that the features till coverage type were extracted directly from the patients table and some features such as 'insurertype', 'coveragestatus', 'copayflag', 'copaypercent' were extracted from reimbursement table.\n", "- Apart from them the other features seem to be engineered features.\n", "- What does these engineered features represent and how were they created. Can you share the data dictionary that provides information about these features.\n", "- There are some features such as 'ocr_insc_amt_covered', 'ocr_perc_insc_amt_covered', 'ocr_insc_amt_covered_12_mo', 'ocr_perc_insc_amt_covered_12_mo' that have 98% of missing values.\n", "- What is the purpose of engineering these features when 98% of the values were missing.\n", "- What does \"Bridging' category in drug type mean? How is it going to be useful and do we need it in our final data?\n", "- We need only patients who are currently on ocrevus therapy and who were discontinued from the ocrevus therapy? Do we also need to have data about patients whose therapy is in pending status?\n", "- What is the difference between coveragetype and insurertype features. Do we need to have both the features?\n", "- What is the difference between previous_ms_therapy_count and previous_ms_therapy_inf_count features. Why do they have same no of null values."]}, {"cell_type": "markdown", "id": "bb441e82-26a3-4aaf-bd6d-fa6be6109cde", "metadata": {}, "source": ["#### Next Steps\n", "\n", "- patient discontinuation based on province(Qubec(QC), BC, Alberta(AB) - pretty high discontinuation rate. focus on these states)\n", "- dayssincefirstinfusion (Current date - Last infusion date. Higher the no of days, higher chances patient might be discontinued.)\n", "- Total no of infusions(how it effects discontinuation of patient, patients are highly likely to switch after multiple infusions & discontinuation rate will be higher after.)\n", "- Explore EDSS score, edss count, mri count."]}, {"cell_type": "code", "execution_count": 38, "id": "7c89dca0-529e-45f5-bd12-1ca5a6d97a97", "metadata": {}, "outputs": [{"data": {"text/plain": ["patients<PERSON><PERSON>tus\n", "OnTherapy                                                           10325\n", "Discontinued-Unknown                                                  789\n", "Discontinued-MD Recommendation                                        541\n", "Discontinued-Switch to another therapy                                511\n", "Discontinued-Personal                                                 206\n", "Discontinued-Patient Experienced Adverse Event with Program Drug      141\n", "Discontinued-Lost to F/U                                               47\n", "Discontinued-Program Drug Lack of Efficacy                             37\n", "Discontinued-Disease Progression                                       30\n", "Discontinued-Financial Concern                                         10\n", "Discontinued-Other                                                      3\n", "Pending-Clinic - Pending Infusion Appointment                           2\n", "Pending-Patient - Pending Enrollment                                    2\n", "Pending-Infusion Scheduled                                              1\n", "Pending-HCP - Pending Infusion Readiness                                1\n", "Name: count, dtype: Int64"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["df['patientsubstatus'].value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": 47, "id": "c19b1697-3dc6-4e8a-a878-93ce4b7ae9e7", "metadata": {}, "outputs": [{"data": {"text/plain": ["(12646, 35)"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": 42, "id": "a6232c14-98ee-43e7-a675-a1d29508b8fd", "metadata": {}, "outputs": [], "source": ["df['patientsubstatus'] = df['patientsubstatus'].apply(lambda x: 'Discontinued' if 'Discontinued' in x else x)"]}, {"cell_type": "code", "execution_count": 43, "id": "f9abb905-c301-459f-b5d3-d4a585149f81", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['OnTherapy', 'Discontinued', 'Pending-Infusion Scheduled',\n", "       'Pending-Patient - Pending Enrollment',\n", "       'Pending-Clinic - Pending Infusion Appointment',\n", "       'Pending-HCP - Pending Infusion Readiness'], dtype=object)"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["df['patientsub<PERSON>tus'].unique()"]}, {"cell_type": "code", "execution_count": 44, "id": "cb9b3994-36d7-4bae-bae6-5fa6955218a7", "metadata": {}, "outputs": [], "source": ["df_new = df[~df['patientsubstatus'].str.contains('Pending')]"]}, {"cell_type": "code", "execution_count": 45, "id": "6e640174-8dbd-4210-9f30-5b7cdd73cc67", "metadata": {}, "outputs": [{"data": {"text/plain": ["(12640, 35)"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["df_new.shape"]}, {"cell_type": "code", "execution_count": 46, "id": "6bf2d389-543e-46d5-9de7-e16db40cada6", "metadata": {}, "outputs": [{"data": {"text/plain": ["patients<PERSON><PERSON>tus\n", "OnTherapy       10325\n", "Discontinued     2315\n", "Name: count, dtype: int64"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["df_new['patientsubstatus'].value_counts()"]}, {"cell_type": "code", "execution_count": 48, "id": "e661e2ee-634d-4c4f-af6b-6d813ce8fb14", "metadata": {}, "outputs": [], "source": ["df_new.to_csv('ocrevus_switch_alerts.csv', index=False)"]}, {"cell_type": "code", "execution_count": 49, "id": "9346d1b4-e7e9-423a-b909-756e680b1680", "metadata": {}, "outputs": [{"data": {"text/plain": ["patientprovince\n", "ON    3797\n", "QC    3585\n", "AB    2284\n", "SK     716\n", "BC     672\n", "MB     553\n", "NB     409\n", "NS     404\n", "NL     108\n", "PE      62\n", "YT      36\n", "NT       9\n", "NU       5\n", "Name: count, dtype: Int64"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["df_new['patientprovince'].value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": 50, "id": "2af1363a-9484-42c4-bcd0-c46a8d2ebe9c", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.countplot(x='patientprovince', hue='patientsubstatus', data=df_new);"]}, {"cell_type": "code", "execution_count": 54, "id": "d96bebdb-df3f-4bbd-b5ad-c6d785bfdb2d", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["df_new['firstinfusiondate'].isna().sum()"]}, {"cell_type": "code", "execution_count": 55, "id": "0a8fcff2-37ea-45f7-9869-aa33792625f5", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['patientid', 'currentphysicianid', 'patientprovince', 'drugtype', 'age',\n", "       'gender', 'lineoftherapy', 'patientstatus', 'patientsubstatus',\n", "       'diagnosis', 'programname', 'subdiagnosis', 'firstinfusiondate',\n", "       'coveragetype', 'discontinue_flag', 'latest_infusion_dt',\n", "       'total_infusions', 'financial_asst_active_flag', 'edss_test_value',\n", "       'edss_count', 'mri_count', 'edss_count_12_mo', 'mri_count_12_mo',\n", "       'insurertype', 'coveragestatus', 'copayflag', 'copaypercent',\n", "       'previous_ms_therapy_count', 'previous_ms_therapy_inf_count',\n", "       'therapyname_ms_off_label_count', 'ocr_insc_amt_covered',\n", "       'ocr_perc_insc_amt_covered', 'ocr_insc_amt_covered_12_mo',\n", "       'ocr_perc_insc_amt_covered_12_mo', 'prescribed_ocrevus_pats'],\n", "      dtype='object')"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["df_new.columns"]}, {"cell_type": "code", "execution_count": 56, "id": "57347d91-21c5-42e4-a2d2-16c22ad2531f", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["df_new['latest_infusion_dt'].isna().sum()"]}, {"cell_type": "code", "execution_count": 57, "id": "34b16e64-bff5-4527-b970-eee4fa4208ef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.series.Series'>\n", "Index: 12640 entries, 0 to 12645\n", "Series name: total_infusions\n", "Non-Null Count  Dtype\n", "--------------  -----\n", "12632 non-null  Int64\n", "dtypes: Int64(1)\n", "memory usage: 209.8 KB\n"]}], "source": ["df_new['total_infusions'].info()"]}, {"cell_type": "code", "execution_count": 60, "id": "5d924319-0b19-4394-94a2-9cc858769291", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(x='total_infusions', data=df_new);"]}, {"cell_type": "code", "execution_count": 73, "id": "f6c656fb-727c-4785-858b-8d0242761850", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 6))\n", "ax = sns.barplot(x='patientsubstatus', y='total_infusions', data=df_new, color='skyblue')\n", "\n", "for i in ax.containers:\n", "    ax.bar_label(i, fmt='%.2f', padding=3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 62, "id": "69b13dd3-5a1a-495e-a9b3-f0eb14<PERSON>daab", "metadata": {}, "outputs": [{"data": {"text/plain": ["patients<PERSON><PERSON>tus\n", "Discontinued    5.586147\n", "OnTherapy       8.032649\n", "Name: total_infusions, dtype: Float64"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["df_new.groupby('patientsubstatus')['total_infusions'].mean()"]}, {"cell_type": "code", "execution_count": 88, "id": "77cd3ad9-2a1b-4f17-842c-ca1d7347f239", "metadata": {}, "outputs": [{"data": {"text/plain": ["314"]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["df_new['edss_test_value'].isna().sum()"]}, {"cell_type": "code", "execution_count": 89, "id": "368f8a4d-fc69-45c4-b50c-d733699b0998", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.series.Series'>\n", "Index: 12640 entries, 0 to 12645\n", "Series name: edss_test_value\n", "Non-Null Count  Dtype \n", "--------------  ----- \n", "12326 non-null  string\n", "dtypes: string(1)\n", "memory usage: 197.5 KB\n"]}], "source": ["df_new['edss_test_value'].info()"]}, {"cell_type": "code", "execution_count": 90, "id": "1eae979a-81e3-45b6-baff-3b03c4204eb5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.series.Series'>\n", "Index: 12640 entries, 0 to 12645\n", "Series name: edss_count\n", "Non-Null Count  Dtype\n", "--------------  -----\n", "12416 non-null  Int64\n", "dtypes: Int64(1)\n", "memory usage: 209.8 KB\n"]}], "source": ["df_new['edss_count'].info()"]}, {"cell_type": "code", "execution_count": 91, "id": "e128c60d-28b3-479e-b486-37493cec1992", "metadata": {}, "outputs": [{"data": {"text/plain": ["224"]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}], "source": ["df_new['edss_count'].isna().sum()"]}, {"cell_type": "code", "execution_count": 92, "id": "077a790d-5e8b-495c-bb5a-39c10d60d81a", "metadata": {}, "outputs": [{"data": {"text/plain": ["edss_count\n", "1    11088\n", "2     1104\n", "3      126\n", "0       84\n", "4       13\n", "5        1\n", "Name: count, dtype: Int64"]}, "execution_count": 92, "metadata": {}, "output_type": "execute_result"}], "source": ["df_new['edss_count'].value_counts()"]}, {"cell_type": "code", "execution_count": 85, "id": "5dbf2132-8ef3-427d-bdb4-b006e00500a4", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.countplot(x='edss_count', hue='patientsubstatus', data=df_new);"]}, {"cell_type": "code", "execution_count": 93, "id": "32dcdfcd-14c0-4d66-9b0f-3c660208f0ca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.series.Series'>\n", "Index: 12640 entries, 0 to 12645\n", "Series name: mri_count\n", "Non-Null Count  Dtype\n", "--------------  -----\n", "12416 non-null  Int64\n", "dtypes: Int64(1)\n", "memory usage: 209.8 KB\n"]}], "source": ["df_new['mri_count'].info()"]}, {"cell_type": "code", "execution_count": 94, "id": "cb62e193-d945-48f8-9268-53daec385c04", "metadata": {}, "outputs": [{"data": {"text/plain": ["224"]}, "execution_count": 94, "metadata": {}, "output_type": "execute_result"}], "source": ["df_new['mri_count'].isna().sum()"]}, {"cell_type": "code", "execution_count": 96, "id": "61a84493-7c56-421d-8e20-48bff433d6cd", "metadata": {}, "outputs": [{"data": {"text/plain": ["mri_count\n", "0    6900\n", "1    4270\n", "2    1170\n", "3      66\n", "4       9\n", "6       1\n", "Name: count, dtype: Int64"]}, "execution_count": 96, "metadata": {}, "output_type": "execute_result"}], "source": ["df_new['mri_count'].value_counts()"]}, {"cell_type": "code", "execution_count": 97, "id": "7e91dd80-8358-4caa-8dce-204b91cd5b47", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.countplot(x='mri_count', hue='patientsubstatus', data=df_new);"]}, {"cell_type": "code", "execution_count": 98, "id": "d10fffa6-2302-4fb1-9f72-369c69750343", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.series.Series'>\n", "Index: 12640 entries, 0 to 12645\n", "Series name: edss_count_12_mo\n", "Non-Null Count  Dtype\n", "--------------  -----\n", "12416 non-null  Int64\n", "dtypes: Int64(1)\n", "memory usage: 209.8 KB\n"]}], "source": ["df_new['edss_count_12_mo'].info()"]}, {"cell_type": "code", "execution_count": 99, "id": "2be59877-f8b1-406b-8b48-a7b4524020d4", "metadata": {}, "outputs": [{"data": {"text/plain": ["edss_count_12_mo\n", "0    11407\n", "1      996\n", "2       13\n", "Name: count, dtype: Int64"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["df_new['edss_count_12_mo'].value_counts()"]}, {"cell_type": "code", "execution_count": 100, "id": "1c78085a-4732-4964-b912-880f558af965", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.countplot(x='edss_count_12_mo', hue='patientsubstatus', data=df_new);"]}, {"cell_type": "code", "execution_count": 101, "id": "196d9b86-f5ff-4912-ac4c-9e7ca25db2fa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.series.Series'>\n", "Index: 12640 entries, 0 to 12645\n", "Series name: mri_count_12_mo\n", "Non-Null Count  Dtype\n", "--------------  -----\n", "12416 non-null  Int64\n", "dtypes: Int64(1)\n", "memory usage: 209.8 KB\n"]}], "source": ["df_new['mri_count_12_mo'].info()"]}, {"cell_type": "code", "execution_count": 102, "id": "9d707087-9b64-4b44-a12f-636c390c2012", "metadata": {}, "outputs": [{"data": {"text/plain": ["mri_count_12_mo\n", "0    12114\n", "1      297\n", "2        5\n", "Name: count, dtype: Int64"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}], "source": ["df_new['mri_count_12_mo'].value_counts()"]}, {"cell_type": "code", "execution_count": 103, "id": "0ef0c9a2-c460-4bc7-96ae-0bf2899254cb", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAk0AAAGxCAYAAAB/QoKnAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjEsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvc2/+5QAAAAlwSFlzAAAPYQAAD2EBqD+naQAAQLlJREFUeJzt3X18THfe//H3ICIkhoRkktIm6i7EXcUSveFyX03Vbrds2WDprduUolZbql1Rim5ZtvyK3uu1La1tNRUtqdbthlzuQlul0TYRNBK3Ccn394fLuToSnKRhJryej8c8tud7Puecz5nMdt79zpkzDmOMEQAAAC6rgqcbAAAAKA8ITQAAADYQmgAAAGwgNAEAANhAaAIAALCB0AQAAGADoQkAAMAGQhMAAIANlTzdwPWksLBQP//8swICAuRwODzdDgAAsMEYo+PHjyssLEwVKlx6PonQVIZ+/vln1a1b19NtAACAUjh48KDq1KlzyfWEpjIUEBAg6fyTXr16dQ93AwAA7MjNzVXdunWt9/FLITSVoQsfyVWvXp3QBABAOXOlS2u4EBwAAMAGQhMAAIANhCYAAAAbuKYJAFCuFRQU6OzZs55uA17Mx8dHFStW/M378Who+vLLLzVjxgylpKQoIyNDy5cvV+/eva31xhg999xzWrBggbKzs9W2bVv94x//UNOmTa2avLw8Pfnkk3r33Xd1+vRpde7cWfPmzXP7ymB2drZGjhypFStWSJJ69eqlOXPmqEaNGlZNenq6hg0bpi+++EJ+fn7q16+fXnrpJVWuXPmqPw8AgJIzxigzM1PHjh3zdCsoB2rUqCGXy/Wb7qPo0dB08uRJtWjRQn/5y190//33F1k/ffp0zZo1S0uWLFHDhg31wgsvqGvXrtq7d6/1tcD4+Hj9+9//1tKlSxUUFKQxY8YoNjZWKSkpVqrs16+ffvzxRyUmJkqSHnnkEcXFxenf//63pPP/lXLPPfeodu3a+uqrr3T06FENHDhQxhjNmTPnGj0bAICSuBCYgoODVbVqVW4qjGIZY3Tq1CllZWVJkkJDQ3/TzryCJLN8+XJrubCw0LhcLjNt2jRr7MyZM8bpdJp//vOfxhhjjh07Znx8fMzSpUutmp9++slUqFDBJCYmGmOM2b17t5FkNm7caNVs2LDBSDJ79uwxxhizcuVKU6FCBfPTTz9ZNe+++67x9fU1OTk5ts8hJyfHSCrRNgCAkjt37pzZvXu3OXLkiKdbQTlx5MgRs3v3bnPu3Lki6+y+f3vtheD79+9XZmamunXrZo35+vqqQ4cOWr9+vSQpJSVFZ8+edasJCwtTVFSUVbNhwwY5nU61bdvWqmnXrp2cTqdbTVRUlMLCwqya7t27Ky8vTykpKVf1PAEAJXfhGqaqVat6uBOUFxdeK7/l+jevvRA8MzNTkhQSEuI2HhISoh9++MGqqVy5smrWrFmk5sL2mZmZCg4OLrL/4OBgt5qLj1OzZk1VrlzZqilOXl6e8vLyrOXc3Fy7pwcAKAN8JAe7yuK14rUzTRdcfJLGmCue+MU1xdWXpuZiCQkJcjqd1oPfnQOAG1PHjh0VHx/v6TauaO3atXI4HFw8X0peG5pcLpckFZnpycrKsmaFXC6X8vPzlZ2dfdmaQ4cOFdn/4cOH3WouPk52drbOnj1bZAbq1yZMmKCcnBzrcfDgwRKeJQCgPLlU6Fi2bJmef/75Mj3WoEGD3L5R7g1KG7oOHDggh8Oh1NTUq9LXteK1oSkiIkIul0tJSUnWWH5+vpKTk9W+fXtJUuvWreXj4+NWk5GRoZ07d1o1MTExysnJ0ebNm62aTZs2KScnx61m586dysjIsGpWrVolX19ftW7d+pI9+vr6Wr8zx+/NAcCNKzAw8Io/9orrwNW4Qt2u48ePm23btplt27YZSWbWrFlm27Zt5ocffjDGGDNt2jTjdDrNsmXLzI4dO8yDDz5oQkNDTW5urrWPxx57zNSpU8esXr3abN261XTq1Mm0aNHC7er4Hj16mObNm5sNGzaYDRs2mGbNmpnY2Fhr/blz50xUVJTp3Lmz2bp1q1m9erWpU6eOGT58eInOh2/PAcC1cfr0abN7925z+vTpEm3XoUMHM2zYMDNs2DDjdDpNYGCgmThxoiksLDTGGPPmm2+a1q1bG39/fxMSEmIefPBBc+jQIWOMMfv37zeS3B4DBw609jtq1CjrOHl5eWbs2LEmLCzMVK1a1fzud78za9assdYvXrzYOJ1Ok5iYaBo3bmyqVatmunfvbn7++WdjjDGTJk0qcqw1a9aYvLw8M2zYMONyuYyvr6+55ZZbzNSpU93627Ztm3Wc7Oxsa1tjjFmzZo2RZD7++GPTvHlz4+vra373u9+Z7du3W9scOHDAxMbGmho1apiqVauaJk2amE8++eSy5//pp5+a22+/3XpO77nnHvPdd99Z+7x4uw4dOhT7vBljzH333Wft1xhj/vGPf5j69esbX19fExwcbO6///6S/Mktl3vN2H3/9mhouvDHu9QfobCw0EyaNMl6cdx1111mx44dbvs4ffq0GT58uAkMDDR+fn4mNjbWpKenu9UcPXrU9O/f3wQEBJiAgADTv39/k52d7Vbzww8/mHvuucf4+fmZwMBAM3z4cHPmzJkSnQ+hCQCujd8Smvz9/c2oUaPMnj17zFtvvWWqVq1qFixYYIwx5rXXXjMrV640+/btMxs2bDDt2rUzd999tzHm/H9gf/DBB0aS2bt3r8nIyDDHjh2z9vvrN/9+/fqZ9u3bmy+//NJ89913ZsaMGcbX19d88803xpjzocnHx8d06dLFbNmyxaSkpJjIyEjTr18/Y8z5SYU+ffqYHj16mIyMDJORkWHy8vLMjBkzTN26dc2XX35pDhw4YNatW2feeecdY0zJQlNkZKRZtWqV2b59u4mNjTXh4eEmPz/fGGPMPffcY7p27Wq2b99u9u3bZ/7973+b5OTky57/+++/bz744APzzTffmG3btpl7773XNGvWzBQUFBhjjNm8ebORZFavXm0yMjLM0aNHi33ejHEPTVu2bDEVK1Y077zzjjlw4IDZunWr+fvf/16iv/kF5T40XW8ITQBwbfyW0BQZGWnNLBljzPjx401kZGSx9Rfe7I8fP26M+b/QcfF/eP/6zf+7774zDofD7d5/xhjTuXNnM2HCBGPM+dAkyW025h//+IcJCQmxlgcOHGjuu+8+t32MGDHCdOrUya3/C0oSmn59f8OjR48aPz8/89577xljjGnWrJmZPHlysc/Hpc7/YllZWUaSNdFRXG/GXDk0ffDBB6Z69epunzCVVlmEJq+95cCNqvXYNzzdgldImTHA0y0AuE61a9fO7ZvRMTExmjlzpgoKCrR9+3ZNnjxZqamp+uWXX1RYWCjp/E9tNWnSxNb+t27dKmOMGjZs6Dael5enoKAga7lq1aq69dZbreXQ0FDrrtWXMmjQIHXt2lWNGjVSjx49FBsb63avQrtiYmKsfw4MDFSjRo2UlpYmSRo5cqQef/xxrVq1Sl26dNH999+v5s2bX3Z/+/bt0zPPPKONGzfqyJEjbs9bVFRUifu7oGvXrrrllltUr1499ejRQz169NDvf/97j92fy2svBAcA4Fo6c+aMunXrJn9/f7311lvasmWLli9fLun8F5HsKiwsVMWKFZWSkqLU1FTrkZaWpr///e9WnY+Pj9t2DodDxpjL7vu2227T/v379fzzz+v06dPq06eP/vjHP0qSKlQ4/5b+632U5EaOF4LkQw89pO+//15xcXHasWOHoqOjr/iTYvfee6+OHj2qhQsXatOmTdq0aZOkKz9vFSpUKHLOv+45ICBAW7du1bvvvqvQ0FA9++yzatGihcdumUBoAgDcUDZu3FhkuUGDBtqzZ4+OHDmiadOm6c4771Tjxo2LzPxc+BH3goKCS+6/VatWKigoUFZWlurXr+/2uHA7HTsqV65c7HGqV6+uvn37auHChXrvvff0wQcf6JdfflHt2rUlye2b4Jf6iv+vn4Ps7Gx98803aty4sTVWt25dPfbYY1q2bJnGjBmjhQsXXvL8jx49qrS0ND399NPq3LmzIiMji9wK6FLPW+3atd36LSgo0M6dO91qKlWqpC5dumj69Onavn27Dhw4oC+++KLY87ra+HgOAHBDOXjwoEaPHq1HH31UW7du1Zw5czRz5kzdfPPNqly5subMmaPHHntMO3fuLHLvpVtuuUUOh0Mff/yxevbsKT8/P/n7+7vVNGzYUP3799eAAQM0c+ZMtWrVSkeOHNEXX3yhZs2aqWfPnrb6DA8P12effaa9e/cqKChITqdTc+fOVWhoqFq2bKkKFSroX//6l1wul2rUqKEKFSqoXbt2mjZtmsLDw3XkyBE9/fTTxe57ypQpCgoKUkhIiCZOnKhatWpZ94SKj4/X3XffrYYNGyo7O1tffPGFIiMjL3n+NWvWVFBQkBYsWKDQ0FClp6frqaeecjtecHCw/Pz8lJiYqDp16qhKlSpyOp3q1KmTRo8erU8++US33nqrZs+e7TaL9PHHH+v777/XXXfdpZo1a2rlypUqLCxUo0aNbD2HZY2ZJgDADWXAgAE6ffq0fve732nYsGEaMWKEHnnkEdWuXVtLlizRv/71LzVp0kTTpk3TSy+95LbtTTfdpOeee05PPfWUQkJCNHz48GKPsXjxYg0YMEBjxoxRo0aN1KtXL23atKlEvxzx8MMPq1GjRoqOjlbt2rX19ddfy9/fXy+++KKio6PVpk0bHThwQCtXrrQ+mlu0aJHOnj2r6OhojRo1Si+88EKx+542bZpGjRql1q1bKyMjQytWrHCbDRo2bJgiIyPVo0cPNWrUSPPmzbvk+VeoUEFLly5VSkqKoqKi9MQTT2jGjBlux6tUqZJeeeUVvfrqqwoLC9N9990nSRo8eLAGDhyoAQMGqEOHDoqIiNB//dd/WdvVqFFDy5YtU6dOnRQZGal//vOfevfdd9W0aVPbz2NZcpgrfYAK23Jzc+V0OpWTk1PqG11yIfh5XAgO4HLOnDmj/fv3KyIiQlWqVLG9XceOHdWyZUu9/PLLV685eKXLvWbsvn8z0wQAAGADoQkAAMAGLgQHANww1q5d6+kWUI4x0wQAAGADoQkAAMAGQhMAAIANhCYAAAAbCE0AAAA2EJoAAABsIDQBAHCdmjx5slq2bOnpNq4bhCYAAK6xgwcPasiQIQoLC1PlypV1yy23aNSoUTp69Kit7ZcsWSKHw3HZB/ekKnvc3BIAcN241r/fWZrfyfz+++8VExOjhg0b6t1331VERIR27dqlsWPH6tNPP9XGjRsVGBh42X307dtXPXr0sJb/8Ic/KCoqSlOmTLHGAgMDr0pwMsaooKBAlSrdeBGCmSYAAK6hYcOGqXLlylq1apU6dOigm2++WXfffbdWr16tn376SRMnTpQkhYeHa+rUqRo8eLACAgJ08803a8GCBZIkPz8/uVwu61G5cmVVrVq1yNgFb775psLDw+V0OvWnP/1Jx48ft9YZYzR9+nTVq1dPfn5+atGihd5//31r/dq1a+VwOPTZZ58pOjpavr6+Wrdunfbt26f77rtPISEh8vf3V5s2bbR69Wq3cw0PD9fzzz+vfv36yd/fX2FhYZozZ461fvDgwYqNjXXb5ty5c3K5XFq0aFHZPellhNAEAMA18ssvv+izzz7T0KFD5efn57bO5XKpf//+eu+992SMkSTNnDlT0dHR2rZtm4YOHarHH39ce/bsKdEx9+3bpw8//FAff/yxPv74YyUnJ2vatGnW+qefflqLFy/W/PnztWvXLj3xxBP685//rOTkZLf9jBs3TgkJCUpLS1Pz5s114sQJ9ezZU6tXr9a2bdvUvXt33XvvvUpPT3fbbsaMGWrevLm2bt2qCRMm6IknnlBSUpIk6aGHHlJiYqIyMjKs+pUrV+rEiRPq06dPic7zWiA0AQBwjXz77bcyxigyMrLY9ZGRkcrOztbhw4clST179tTQoUNVv359jR8/XrVq1SrxR26FhYVasmSJoqKidOeddyouLk6ff/65JOnkyZOaNWuWFi1apO7du6tevXoaNGiQ/vznP+vVV19128+UKVPUtWtX3XrrrQoKClKLFi306KOPqlmzZmrQoIFeeOEF1atXTytWrHDb7vbbb9dTTz2lhg0basSIEfrjH/+o2bNnS5Lat2+vRo0a6c0337TqFy9erAceeED+/v4lOs9rgdAEAICXuDDD5HA4JEnNmze31jkcDrlcLmVlZZVon+Hh4QoICLCWQ0NDrX3s3r1bZ86cUdeuXeXv72893njjDe3bt89tP9HR0W7LJ0+e1Lhx49SkSRPVqFFD/v7+2rNnT5GZppiYmCLLaWlp1vJDDz2kxYsXS5KysrL0ySefaPDgwSU6x2vlxruKCwAAD6lfv74cDod2796t3r17F1m/Z88e1axZU7Vq1ZIk+fj4uK13OBwqLCws0TEvt48L//vJJ5/opptucqvz9fV1W65WrZrb8tixY/XZZ5/ppZdeUv369eXn56c//vGPys/Pv2JPF0KhJA0YMEBPPfWUNmzYoA0bNig8PFx33nmn/RO8hghNAABcI0FBQeratavmzZunJ554wu26pszMTL399tsaMGCAW6i4mpo0aSJfX1+lp6erQ4cOJdp23bp1GjRokH7/+99Lkk6cOKEDBw4Uqdu4cWOR5caNG1vLQUFB6t27txYvXqwNGzboL3/5S8lP5BohNAEAcA3NnTtX7du3V/fu3fXCCy+43XLgpptu0t/+9rdr1ktAQICefPJJPfHEEyosLNQdd9yh3NxcrV+/Xv7+/ho4cOAlt61fv76WLVume++9Vw6HQ88880yxs2Bff/21pk+frt69eyspKUn/+te/9Mknn7jVPPTQQ4qNjVVBQcFlj+lphCYAAK6hBg0a6D//+Y8mT56svn376ujRo3K5XOrdu7cmTZp0xXs0lbXnn39ewcHBSkhI0Pfff68aNWrotttu01//+tfLbjd79mwNHjxY7du3V61atTR+/Hjl5uYWqRszZoxSUlL03HPPKSAgQDNnzlT37t3darp06aLQ0FA1bdpUYWFhZXp+ZclhLlx1ht8sNzdXTqdTOTk5ql69eqn2ca1vzOatSnPDOAA3jjNnzmj//v2KiIhQlSpVPN0OLiE8PFzx8fGKj4+/bN2pU6cUFhamRYsW6Q9/+MNV6eVyrxm779/MNAEAAI8oLCxUZmamZs6cKafTqV69enm6pcsiNAEAAI9IT09XRESE6tSpoyVLlnj9T7N4d3cAAKDcKu7bdL8WHh6u8nSVEDe3BAAAsIHQBAAAYAOhCQAAwAZCEwAAgA2EJgAAABsITQAAADYQmgAA8EIOh0Mffvihp9u4rEGDBql3796ebkOS1LFjxyveefy3IjQBAHANDRo0SA6HQw6HQz4+PgoJCVHXrl21aNEitx+8zcjI0N133+3BTv/PgQMH5HA4lJqa6jb+97//XUuWLPFIT57AzS0BANeN9CnNrunxbn52R6m269GjhxYvXqyCggIdOnRIiYmJGjVqlN5//32tWLFClSpVksvlKuNuy57T6fR0C9cUM00AAFxjvr6+crlcuummm3Tbbbfpr3/9qz766CN9+umn1szNrz+ey8/P1/DhwxUaGqoqVaooPDxcCQkJ1v6OHTumRx55RCEhIapSpYqioqL08ccfW+s/+OADNW3aVL6+vgoPD9fMmTPd+gkPD9fUqVM1ePBgBQQE6Oabb9aCBQus9REREZKkVq1ayeFwqGPHjpKKfjzXsWNHjRw5UuPGjVNgYKBcLpcmT55srS9uxurYsWNyOBxau3atNbZ792717NlT/v7+CgkJUVxcnI4cOWKtP3nypAYMGCB/f3+FhoYWOZ+rhdAEAIAX6NSpk1q0aKFly5YVWffKK69oxYoV+u///m/t3btXb731lsLDwyWd/9Hbu+++W+vXr9dbb72l3bt3a9q0aapYsaIkKSUlRX369NGf/vQn7dixQ5MnT9YzzzxT5GO1mTNnKjo6Wtu2bdPQoUP1+OOPa8+ePZKkzZs3S5JWr16tjIyMYnu84PXXX1e1atW0adMmTZ8+XVOmTFFSUpLt5yEjI0MdOnRQy5Yt9Z///EeJiYk6dOiQ+vTpY9WMHTtWa9as0fLly7Vq1SqtXbtWKSkpto9RWnw8BwCAl2jcuLG2b99eZDw9PV0NGjTQHXfcIYfDoVtuucVat3r1am3evFlpaWlq2LChJKlevXrW+lmzZqlz58565plnJEkNGzbU7t27NWPGDA0aNMiq69mzp4YOHSpJGj9+vGbPnq21a9eqcePGql27tiQpKCjoih8bNm/eXJMmTZIkNWjQQHPnztXnn3+url272noO5s+fr9tuu01Tp061xhYtWqS6devqm2++UVhYmF577TW98cYb1j5ff/111alTx9b+fwtmmgAA8BLGGDkcjiLjgwYNUmpqqho1aqSRI0dq1apV1rrU1FTVqVPHCkwXS0tL0+233+42dvvtt+vbb79VQUGBNda8eXPrnx0Oh1wul7Kyskp8Dr/ejySFhoaWaD8pKSlas2aN/P39rUfjxo0lSfv27dO+ffuUn5+vmJgYa5vAwEA1atSoxL2WFDNNAAB4ibS0NOv6oV+77bbbtH//fn366adavXq1+vTpoy5duuj999+Xn5/fZfdZXBAzxhSp8/HxcVt2OBxu3+az63L7qVChQpHjnz171q2+sLBQ9957r1588cUi+w4NDdW3335b4p7KCjNNAAB4gS+++EI7duzQ/fffX+z66tWrq2/fvlq4cKHee+89ffDBB/rll1/UvHlz/fjjj/rmm2+K3a5Jkyb66quv3MbWr1+vhg0bWtc9XUnlypUlyW1mqjQufMyXkZFhjV18G4PbbrtNu3btUnh4uOrXr+/2qFatmurXry8fHx9t3LjR2iY7O/uS51+WmGkCAOAay8vLU2ZmptstBxISEhQbG6sBAwYUqZ89e7ZCQ0PVsmVLVahQQf/617/kcrlUo0YNdejQQXfddZfuv/9+zZo1S/Xr19eePXvkcDjUo0cPjRkzRm3atNHzzz+vvn37asOGDZo7d67mzZtnu9/g4GD5+fkpMTFRderUUZUqVUp1uwE/Pz+1a9dO06ZNU3h4uI4cOaKnn37arWbYsGFauHChHnzwQY0dO1a1atXSd999p6VLl2rhwoXy9/fXkCFDNHbsWAUFBSkkJEQTJ060ZrGuJmaaAAC4xhITExUaGqrw8HD16NFDa9as0SuvvKKPPvqo2Nkff39/vfjii4qOjlabNm104MABrVy50goKH3zwgdq0aaMHH3xQTZo00bhx46xZodtuu03//d//raVLlyoqKkrPPvuspkyZ4nYR+JVUqlRJr7zyil599VWFhYXpvvvuK/W5L1q0SGfPnlV0dLRGjRqlF154wW19WFiYvv76axUUFKh79+6KiorSqFGj5HQ6rfOdMWOG7rrrLvXq1UtdunTRHXfcodatW5e6J7scprgPNlEqubm5cjqdysnJUfXq1Uu1j9Zj3yjjrsqnlBlF/0sLAC44c+aM9u/fr4iICFWpUsXT7aAcuNxrxu77NzNNAAAANhCaAAAAbCA0AQAA2EBoAgAAsIHQBAAAYAOhCQBQbvEFcNhVFq8VQhMAoNy58FMdp06d8nAnKC8uvFYu/pmXkuCO4ACAcqdixYqqUaOG9UOwVatWLfaHbgFjjE6dOqWsrCzVqFHD9k/HFIfQBAAol1wulyRZwQm4nBo1alivmdIiNAEAyiWHw6HQ0FAFBwfr7Nmznm4HXszHx+c3zTBdQGgCAJRrFStWLJM3ROBKuBAcAADABkITAACADV4dms6dO6enn35aERER8vPzU7169TRlyhQVFhZaNcYYTZ48WWFhYfLz81PHjh21a9cut/3k5eVpxIgRqlWrlqpVq6ZevXrpxx9/dKvJzs5WXFycnE6nnE6n4uLidOzYsWtxmgAAoBzw6tD04osv6p///Kfmzp2rtLQ0TZ8+XTNmzNCcOXOsmunTp2vWrFmaO3eutmzZIpfLpa5du+r48eNWTXx8vJYvX66lS5fqq6++0okTJxQbG6uCggKrpl+/fkpNTVViYqISExOVmpqquLi4a3q+AADAezmMF99ONTY2ViEhIXrttdessfvvv19Vq1bVm2++KWOMwsLCFB8fr/Hjx0s6P6sUEhKiF198UY8++qhycnJUu3Ztvfnmm+rbt68k6eeff1bdunW1cuVKde/eXWlpaWrSpIk2btyotm3bSpI2btyomJgY7dmzR40aNbLVb25urpxOp3JyclS9evVSnXPrsW+UarvrTcqMAZ5uAQBwg7D7/u3VM0133HGHPv/8c33zzTeSpP/5n//RV199pZ49e0qS9u/fr8zMTHXr1s3axtfXVx06dND69eslSSkpKTp79qxbTVhYmKKioqyaDRs2yOl0WoFJktq1ayen02nVFCcvL0+5ubluDwAAcH3y6lsOjB8/Xjk5OWrcuLEqVqyogoIC/e1vf9ODDz4oScrMzJQkhYSEuG0XEhKiH374waqpXLmyatasWaTmwvaZmZkKDg4ucvzg4GCrpjgJCQl67rnnSn+CAACg3PDqmab33ntPb731lt555x1t3bpVr7/+ul566SW9/vrrbnUX3zrfGHPF2+lfXFNc/ZX2M2HCBOXk5FiPgwcP2jktAABQDnn1TNPYsWP11FNP6U9/+pMkqVmzZvrhhx+UkJCggQMHWrdDz8zMVGhoqLVdVlaWNfvkcrmUn5+v7Oxst9mmrKwstW/f3qo5dOhQkeMfPny4yCzWr/n6+srX1/e3nygAAPB6Xj3TdOrUKVWo4N5ixYoVrVsOREREyOVyKSkpyVqfn5+v5ORkKxC1bt1aPj4+bjUZGRnauXOnVRMTE6OcnBxt3rzZqtm0aZNycnKsGgAAcGPz6pmme++9V3/729908803q2nTptq2bZtmzZqlwYMHSzr/kVp8fLymTp2qBg0aqEGDBpo6daqqVq2qfv36SZKcTqeGDBmiMWPGKCgoSIGBgXryySfVrFkzdenSRZIUGRmpHj166OGHH9arr74qSXrkkUcUGxtr+5tzAADg+ubVoWnOnDl65plnNHToUGVlZSksLEyPPvqonn32Watm3LhxOn36tIYOHars7Gy1bdtWq1atUkBAgFUze/ZsVapUSX369NHp06fVuXNnLVmyxO23it5++22NHDnS+pZdr169NHfu3Gt3sgAAwKt59X2ayhvu01R2uE8TAOBauS7u0wQAAOAtCE0AAAA2EJoAAABsIDQBAADYQGgCAACwgdAEAABgA6EJAADABkITAACADYQmAAAAGwhNAAAANhCaAAAAbCA0AQAA2EBoAgAAsIHQBAAAYAOhCQAAwAZCEwAAgA2EJgAAABsITQAAADYQmgAAAGwgNAEAANhAaAIAALCB0AQAAGADoQkAAMAGQhMAAIANhCYAAAAbCE0AAAA2EJoAAABsIDQBAADYQGgCAACwgdAEAABgA6EJAADABkITAACADYQmAAAAGwhNAAAANhCaAAAAbCA0AQAA2EBoAgAAsIHQBAAAYAOhCQAAwAZCEwAAgA2EJgAAABsITQAAADYQmgAAAGwgNAEAANhAaAIAALCB0AQAAGADoQkAAMAGQhMAAIANhCYAAAAbCE0AAAA2EJoAAABsIDQBAADYQGgCAACwgdAEAABgA6EJAADABkITAACADYQmAAAAGwhNAAAANhCaAAAAbCA0AQAA2OD1oemnn37Sn//8ZwUFBalq1apq2bKlUlJSrPXGGE2ePFlhYWHy8/NTx44dtWvXLrd95OXlacSIEapVq5aqVaumXr166ccff3Sryc7OVlxcnJxOp5xOp+Li4nTs2LFrcYoAAKAc8OrQlJ2drdtvv10+Pj769NNPtXv3bs2cOVM1atSwaqZPn65Zs2Zp7ty52rJli1wul7p27arjx49bNfHx8Vq+fLmWLl2qr776SidOnFBsbKwKCgqsmn79+ik1NVWJiYlKTExUamqq4uLiruXpAgAAL+YwxhhPN3EpTz31lL7++mutW7eu2PXGGIWFhSk+Pl7jx4+XdH5WKSQkRC+++KIeffRR5eTkqHbt2nrzzTfVt29fSdLPP/+sunXrauXKlerevbvS0tLUpEkTbdy4UW3btpUkbdy4UTExMdqzZ48aNWpkq9/c3Fw5nU7l5OSoevXqpTrn1mPfKNV215uUGQM83QIA4AZh9/3bq2eaVqxYoejoaD3wwAMKDg5Wq1attHDhQmv9/v37lZmZqW7dulljvr6+6tChg9avXy9JSklJ0dmzZ91qwsLCFBUVZdVs2LBBTqfTCkyS1K5dOzmdTqsGAADc2Lw6NH3//feaP3++GjRooM8++0yPPfaYRo4cqTfeOD8bk5mZKUkKCQlx2y4kJMRal5mZqcqVK6tmzZqXrQkODi5y/ODgYKumOHl5ecrNzXV7AACA61MlTzdwOYWFhYqOjtbUqVMlSa1atdKuXbs0f/58DRjwfx/fOBwOt+2MMUXGLnZxTXH1V9pPQkKCnnvuOVvnAgAAyjevnmkKDQ1VkyZN3MYiIyOVnp4uSXK5XJJUZDYoKyvLmn1yuVzKz89Xdnb2ZWsOHTpU5PiHDx8uMov1axMmTFBOTo71OHjwYAnPEAAAlBdeHZpuv/127d27123sm2++0S233CJJioiIkMvlUlJSkrU+Pz9fycnJat++vSSpdevW8vHxcavJyMjQzp07rZqYmBjl5ORo8+bNVs2mTZuUk5Nj1RTH19dX1atXd3sAAIDrk1d/PPfEE0+offv2mjp1qvr06aPNmzdrwYIFWrBggaTzH6nFx8dr6tSpatCggRo0aKCpU6eqatWq6tevnyTJ6XRqyJAhGjNmjIKCghQYGKgnn3xSzZo1U5cuXSSdn73q0aOHHn74Yb366quSpEceeUSxsbG2vzkHAACub14dmtq0aaPly5drwoQJmjJliiIiIvTyyy+rf//+Vs24ceN0+vRpDR06VNnZ2Wrbtq1WrVqlgIAAq2b27NmqVKmS+vTpo9OnT6tz585asmSJKlasaNW8/fbbGjlypPUtu169emnu3LnX7mQBAIBX8+r7NJU33Kep7HCfJgDAtXJd3KcJAADAWxCaAAAAbCA0AQAA2EBoAgAAsIHQBAAAYAOhCQAAwIZShaZOnTrp2LFjRcZzc3PVqVOn39oTAACA1ylVaFq7dq3y8/OLjJ85c0br1q37zU0BAAB4mxLdEXz79u3WP+/evdvth3ILCgqUmJiom266qey6AwAA8BIlCk0tW7aUw+GQw+Eo9mM4Pz8/zZkzp8yaAwAA8BYlCk379++XMUb16tXT5s2bVbt2bWtd5cqVFRwc7PZ7bgAAANeLEoWmW265RZJUWFh4VZoBAADwViUKTb/2zTffaO3atcrKyioSop599tnf3BgAAIA3KVVoWrhwoR5//HHVqlVLLpdLDofDWudwOAhNAADgulOq0PTCCy/ob3/7m8aPH1/W/QAAAHilUt2nKTs7Ww888EBZ9wIAAOC1ShWaHnjgAa1ataqsewEAAPBapfp4rn79+nrmmWe0ceNGNWvWTD4+Pm7rR44cWSbNAQAAeItShaYFCxbI399fycnJSk5OdlvncDgITQAA4LpTqtC0f//+su4DAADAq5XqmiYAAIAbTalmmgYPHnzZ9YsWLSpVMwAAAN6qVKEpOzvbbfns2bPauXOnjh07VuwP+QIAAJR3pQpNy5cvLzJWWFiooUOHql69er+5KQAAAG9TZtc0VahQQU888YRmz55dVrsEAADwGmV6Ifi+fft07ty5stwlAACAVyjVx3OjR492WzbGKCMjQ5988okGDhxYJo0BAAB4k1KFpm3btrktV6hQQbVr19bMmTOv+M06AACA8qhUoWnNmjVl3QcAAIBXK1VouuDw4cPau3evHA6HGjZsqNq1a5dVXwAAAF6lVBeCnzx5UoMHD1ZoaKjuuusu3XnnnQoLC9OQIUN06tSpsu4RAADA40oVmkaPHq3k5GT9+9//1rFjx3Ts2DF99NFHSk5O1pgxY8q6RwAAAI8r1cdzH3zwgd5//3117NjRGuvZs6f8/PzUp08fzZ8/v6z6AwAA8Aqlmmk6deqUQkJCiowHBwfz8RwAALgulSo0xcTEaNKkSTpz5ow1dvr0aT333HOKiYkps+YAAAC8Rak+nnv55Zd19913q06dOmrRooUcDodSU1Pl6+urVatWlXWPAAAAHleq0NSsWTN9++23euutt7Rnzx4ZY/SnP/1J/fv3l5+fX1n3CAAA4HGlCk0JCQkKCQnRww8/7Da+aNEiHT58WOPHjy+T5gAAALxFqa5pevXVV9W4ceMi402bNtU///nP39wUAACAtylVaMrMzFRoaGiR8dq1aysjI+M3NwUAAOBtShWa6tatq6+//rrI+Ndff62wsLDf3BQAAIC3KdU1TQ899JDi4+N19uxZderUSZL0+eefa9y4cdwRHAAAXJdKFZrGjRunX375RUOHDlV+fr4kqUqVKho/frwmTJhQpg0CAAB4g1KFJofDoRdffFHPPPOM0tLS5OfnpwYNGsjX17es+wMAAPAKpQpNF/j7+6tNmzZl1QsAAIDXKtWF4AAAADcaQhMAAIANhCYAAAAbCE0AAAA2EJoAAABsIDQBAADYQGgCAACwgdAEAABgA6EJAADABkITAACADYQmAAAAGwhNAAAANhCaAAAAbCA0AQAA2EBoAgAAsIHQBAAAYEO5Ck0JCQlyOByKj4+3xowxmjx5ssLCwuTn56eOHTtq165dbtvl5eVpxIgRqlWrlqpVq6ZevXrpxx9/dKvJzs5WXFycnE6nnE6n4uLidOzYsWtwVgAAoDwoN6Fpy5YtWrBggZo3b+42Pn36dM2aNUtz587Vli1b5HK51LVrVx0/ftyqiY+P1/Lly7V06VJ99dVXOnHihGJjY1VQUGDV9OvXT6mpqUpMTFRiYqJSU1MVFxd3zc4PAAB4t3IRmk6cOKH+/ftr4cKFqlmzpjVujNHLL7+siRMn6g9/+IOioqL0+uuv69SpU3rnnXckSTk5OXrttdc0c+ZMdenSRa1atdJbb72lHTt2aPXq1ZKktLQ0JSYm6v/9v/+nmJgYxcTEaOHChfr444+1d+9ej5wzAADwLuUiNA0bNkz33HOPunTp4ja+f/9+ZWZmqlu3btaYr6+vOnTooPXr10uSUlJSdPbsWbeasLAwRUVFWTUbNmyQ0+lU27ZtrZp27drJ6XRaNcXJy8tTbm6u2wMAAFyfKnm6gStZunSptm7dqi1bthRZl5mZKUkKCQlxGw8JCdEPP/xg1VSuXNlthupCzYXtMzMzFRwcXGT/wcHBVk1xEhIS9Nxzz5XshAAAQLnk1TNNBw8e1KhRo/TWW2+pSpUql6xzOBxuy8aYImMXu7imuPor7WfChAnKycmxHgcPHrzsMQEAQPnl1aEpJSVFWVlZat26tSpVqqRKlSopOTlZr7zyiipVqmTNMF08G5SVlWWtc7lcys/PV3Z29mVrDh06VOT4hw8fLjKL9Wu+vr6qXr262wMAAFyfvDo0de7cWTt27FBqaqr1iI6OVv/+/ZWamqp69erJ5XIpKSnJ2iY/P1/Jyclq3769JKl169by8fFxq8nIyNDOnTutmpiYGOXk5Gjz5s1WzaZNm5STk2PVAACAG5tXX9MUEBCgqKgot7Fq1aopKCjIGo+Pj9fUqVPVoEEDNWjQQFOnTlXVqlXVr18/SZLT6dSQIUM0ZswYBQUFKTAwUE8++aSaNWtmXVgeGRmpHj166OGHH9arr74qSXrkkUcUGxurRo0aXcMzBgAA3sqrQ5Md48aN0+nTpzV06FBlZ2erbdu2WrVqlQICAqya2bNnq1KlSurTp49Onz6tzp07a8mSJapYsaJV8/bbb2vkyJHWt+x69eqluXPnXvPzAQAA3slhjDGebuJ6kZubK6fTqZycnFJf39R67Btl3FX5lDJjgKdbAADcIOy+f3v1NU0AAADegtAEAABgA6EJAADABkITAACADYQmAAAAGwhNAAAANhCaAAAAbCA0AQAA2EBoAgAAsIHQBAAAYAOhCQAAwAZCEwAAgA2EJgAAABsITQAAADYQmgAAAGwgNAEAANhAaAIAALCB0AQAAGADoQkAAMAGQhMAAIANhCYAAAAbCE0AAAA2EJoAAABsIDQBAADYQGgCAACwgdAEAABgA6EJAADABkITAACADYQmAAAAGwhNAAAANhCaAAAAbCA0AQAA2EBoAgAAsIHQBAAAYAOhCQAAwAZCEwAAgA2EJgAAABsITQAAADYQmgAAAGwgNAEAANhAaAIAALCB0AQAAGADoQkAAMAGQhMAAIANhCYAAAAbCE0AAAA2EJoAAABsIDQBAADYQGgCAACwgdAEAABgA6EJAADABkITAACADYQmAAAAGwhNAAAANhCaAAAAbKjk6QaA4qRPaebpFrzCzc/u8HQLAID/xUwTAACADYQmAAAAGwhNAAAANhCaAAAAbCA0AQAA2ODVoSkhIUFt2rRRQECAgoOD1bt3b+3du9etxhijyZMnKywsTH5+furYsaN27drlVpOXl6cRI0aoVq1aqlatmnr16qUff/zRrSY7O1txcXFyOp1yOp2Ki4vTsWPHrvYpAgCAcsKrQ1NycrKGDRumjRs3KikpSefOnVO3bt108uRJq2b69OmaNWuW5s6dqy1btsjlcqlr1646fvy4VRMfH6/ly5dr6dKl+uqrr3TixAnFxsaqoKDAqunXr59SU1OVmJioxMREpaamKi4u7pqeLwAA8F4OY4zxdBN2HT58WMHBwUpOTtZdd90lY4zCwsIUHx+v8ePHSzo/qxQSEqIXX3xRjz76qHJyclS7dm29+eab6tu3ryTp559/Vt26dbVy5Up1795daWlpatKkiTZu3Ki2bdtKkjZu3KiYmBjt2bNHjRo1stVfbm6unE6ncnJyVL169VKdY+uxb5Rqu+vN8oAZnm7BK3CfJgC4+uy+f3v1TNPFcnJyJEmBgYGSpP379yszM1PdunWzanx9fdWhQwetX79ekpSSkqKzZ8+61YSFhSkqKsqq2bBhg5xOpxWYJKldu3ZyOp1WTXHy8vKUm5vr9gAAANenchOajDEaPXq07rjjDkVFRUmSMjMzJUkhISFutSEhIda6zMxMVa5cWTVr1rxsTXBwcJFjBgcHWzXFSUhIsK6Bcjqdqlu3bulPEAAAeLVyE5qGDx+u7du369133y2yzuFwuC0bY4qMXezimuLqr7SfCRMmKCcnx3ocPHjwSqcBAADKqXIRmkaMGKEVK1ZozZo1qlOnjjXucrkkqchsUFZWljX75HK5lJ+fr+zs7MvWHDp0qMhxDx8+XGQW69d8fX1VvXp1twcAALg+eXVoMsZo+PDhWrZsmb744gtFRES4rY+IiJDL5VJSUpI1lp+fr+TkZLVv316S1Lp1a/n4+LjVZGRkaOfOnVZNTEyMcnJytHnzZqtm06ZNysnJsWoAAMCNrZKnG7icYcOG6Z133tFHH32kgIAAa0bJ6XTKz89PDodD8fHxmjp1qho0aKAGDRpo6tSpqlq1qvr162fVDhkyRGPGjFFQUJACAwP15JNPqlmzZurSpYskKTIyUj169NDDDz+sV199VZL0yCOPKDY21vY35wAAwPXNq0PT/PnzJUkdO3Z0G1+8eLEGDRokSRo3bpxOnz6toUOHKjs7W23bttWqVasUEBBg1c+ePVuVKlVSnz59dPr0aXXu3FlLlixRxYoVrZq3335bI0eOtL5l16tXL82dO/fqniAAACg3ytV9mrwd92kqO9yn6Tzu0wQAV991eZ8mAAAATyE0AQAA2EBoAgAAsIHQBAAAYAOhCQAAwAZCEwAAgA2EJgAAABsITQAAADYQmgAAAGwgNAEAANhAaAIAALCB0AQAAGADoQkAAMAGQhMAAIANhCYAAAAbCE0AAAA2EJoAAABsIDQBAADYQGgCAACwgdAEAABgA6EJAADABkITAACADYQmAAAAGwhNAAAANhCaAAAAbCA0AQAA2EBoAgAAsIHQBAAAYAOhCQAAwAZCEwAAgA2EJgAAABsITQAAADYQmgAAAGwgNAEAANhAaAIAALCB0AQAAGADoQkAAMAGQhMAAIANhCYAAAAbCE0AAAA2EJoAAABsIDQBAADYQGgCAACwgdAEAABgA6EJAADABkITAACADYQmAAAAGwhNAAAANhCaAAAAbCA0AQAA2EBoAgAAsIHQBAAAYAOhCQAAwAZCEwAAgA2VPN0AAO/Xeuwbnm7BK6TMGODpFgB4EDNNAAAANhCaAAAAbCA0AQAA2EBoAgAAsIHQBAAAYAOh6SLz5s1TRESEqlSpotatW2vdunWebgkAAHgBQtOvvPfee4qPj9fEiRO1bds23Xnnnbr77ruVnp7u6dYAAICHEZp+ZdasWRoyZIgeeughRUZG6uWXX1bdunU1f/58T7cGAAA8jND0v/Lz85WSkqJu3bq5jXfr1k3r16/3UFcAAMBbcEfw/3XkyBEVFBQoJCTEbTwkJESZmZnFbpOXl6e8vDxrOScnR5KUm5tb6j4K8k6XetvryXGfAk+34BV+y2upLPG6PG/XX5t4ugWvUPepjZ5uQXc9/a6nW/AKX77woKdbuC5c+HetMeaydYSmizgcDrdlY0yRsQsSEhL03HPPFRmvW7fuVentRhLl6Qa8RYLT0x3gV3hd/i9el17DOecxT7dwXTl+/Liczku/vglN/6tWrVqqWLFikVmlrKysIrNPF0yYMEGjR4+2lgsLC/XLL78oKCjokkELV5abm6u6devq4MGDql69uqfbASTxuoT34TVZdowxOn78uMLCwi5bR2j6X5UrV1br1q2VlJSk3//+99Z4UlKS7rvvvmK38fX1la+vr9tYjRo1rmabN5Tq1avzLwJ4HV6X8Da8JsvG5WaYLiA0/cro0aMVFxen6OhoxcTEaMGCBUpPT9djjzH9CQDAjY7Q9Ct9+/bV0aNHNWXKFGVkZCgqKkorV67ULbfc4unWAACAhxGaLjJ06FANHTrU023c0Hx9fTVp0qQiH30CnsTrEt6G1+S15zBX+n4dAAAAuLklAACAHYQmAAAAGwhNAAAANhCa4HXmzZuniIgIValSRa1bt9a6des83RJuYF9++aXuvfdehYWFyeFw6MMPP/R0S7jBJSQkqE2bNgoICFBwcLB69+6tvXv3erqtGwKhCV7lvffeU3x8vCZOnKht27bpzjvv1N1336309HRPt4Yb1MmTJ9WiRQvNnTvX060AkqTk5GQNGzZMGzduVFJSks6dO6du3brp5MmTnm7tuse35+BV2rZtq9tuu03z58+3xiIjI9W7d28lJCR4sDPg/G9TLl++XL179/Z0K4Dl8OHDCg4OVnJysu666y5Pt3NdY6YJXiM/P18pKSnq1q2b23i3bt20fv16D3UFAN4tJydHkhQYGOjhTq5/hCZ4jSNHjqigoKDIDySHhIQU+SFlAMD5H5odPXq07rjjDkVFRXm6nesedwSH13E4HG7LxpgiYwAAafjw4dq+fbu++uorT7dyQyA0wWvUqlVLFStWLDKrlJWVVWT2CQBudCNGjNCKFSv05Zdfqk6dOp5u54bAx3PwGpUrV1br1q2VlJTkNp6UlKT27dt7qCsA8C7GGA0fPlzLli3TF198oYiICE+3dMNgpgleZfTo0YqLi1N0dLRiYmK0YMECpaen67HHHvN0a7hBnThxQt999521vH//fqWmpiowMFA333yzBzvDjWrYsGF655139NFHHykgIMCanXc6nfLz8/Nwd9c3bjkArzNv3jxNnz5dGRkZioqK0uzZs/kaLTxm7dq1+q//+q8i4wMHDtSSJUuufUO44V3qGs/Fixdr0KBB17aZGwyhCQAAwAauaQIAALCB0AQAAGADoQkAAMAGQhMAAIANhCYAAAAbCE0AAAA2EJoAAABsIDQBAADYQGgC4FGTJ09Wy5YtPd0GAFwRoQmARz355JP6/PPPPd1GiTgcDn344Ye26zMyMtSvXz81atRIFSpUUHx8fJGahQsX6s4771TNmjVVs2ZNdenSRZs3by67pgH8ZoQmAB5hjNG5c+fk7++voKAgT7dzVeXl5al27dqaOHGiWrRoUWzN2rVr9eCDD2rNmjXasGGDbr75ZnXr1k0//fTTNe4WwKUQmgCUSseOHTVixAjFx8erZs2aCgkJ0YIFC3Ty5En95S9/UUBAgG699VZ9+umnks6HAofDoc8++0zR0dHy9fXVunXrSvzx3KJFi9S0aVP5+voqNDRUw4cPt9alp6frvvvuk7+/v6pXr64+ffro0KFD1vpBgwapd+/ebvuLj49Xx44d3c5r5MiRGjdunAIDA+VyuTR58mRrfXh4uCTp97//vRwOh7V8OeHh4fr73/+uAQMGyOl0Flvz9ttva+jQoWrZsqUaN26shQsXqrCw0PYsXEn/HhckJyfrd7/7nfV8PvXUUzp37pytYwI3GkITgFJ7/fXXVatWLW3evFkjRozQ448/rgceeEDt27fX1q1b1b17d8XFxenUqVPWNuPGjVNCQoLS0tLUvHnzEh1v/vz5GjZsmB555BHt2LFDK1asUP369SWdn7nq3bu3fvnlFyUnJyspKUn79u1T3759S3Ve1apV06ZNmzR9+nRNmTJFSUlJkqQtW7ZIOv+L8hkZGdZyWTt16pTOnj2rwMDAEvVdkr/HTz/9pJ49e6pNmzb6n//5H82fP1+vvfaaXnjhhatyTkC5ZwCgFDp06GDuuOMOa/ncuXOmWrVqJi4uzhrLyMgwksyGDRvMmjVrjCTz4Ycfuu1n0qRJpkWLFraOGRYWZiZOnFjsulWrVpmKFSua9PR0a2zXrl1Gktm8ebMxxpiBAwea++67z227UaNGmQ4dOlzyvIwxpk2bNmb8+PHWsiSzfPlyWz1frEOHDmbUqFFXrBs6dKi59dZbzenTp23vtyR/D2OM+etf/2oaNWpkCgsLrZp//OMfxt/f3xQUFNg8I+DGwUwTgFL79UxRxYoVFRQUpGbNmlljISEhkqSsrCxrLDo6ulTHysrK0s8//6zOnTsXuz4tLU1169ZV3bp1rbEmTZqoRo0aSktLK9GxLp4BCw0NdTuHq2369Ol69913tWzZMlWpUsX2diX9e6SlpSkmJkYOh8Oquf3223XixAn9+OOPv/U0gOtOJU83AKD88vHxcVt2OBxuYxfejAsLC62xatWqlepYfn5+l11vjHF78y9uvEKFCjLGuK0/e/ZskW2KO69fn8PV9NJLL2nq1KlavXp1iT++LOnfo7jn7MLzU9xzCdzomGkCUC4EBAQoPDz8khdGN2nSROnp6Tp48KA1tnv3buXk5CgyMlKSVLt2bWVkZLhtl5qaWuJefHx8VFBQUOLtrmTGjBl6/vnnlZiYWOoZuZJo0qSJ1q9f7xYk169fr4CAAN10001X/fhAeUNoAlBuTJ48WTNnztQrr7yib7/9Vlu3btWcOXMkSV26dFHz5s3Vv39/bd26VZs3b9aAAQPUoUMHK4B06tRJ//nPf/TGG2/o22+/1aRJk7Rz584S93EhvGVmZio7O9vWNqmpqUpNTdWJEyd0+PBhpaamavfu3db66dOn6+mnn9aiRYsUHh6uzMxMZWZm6sSJEyXuz66hQ4fq4MGDGjFihPbs2aOPPvpIkyZN0ujRo1WhAm8PwMX4fwWAcmPgwIF6+eWXNW/ePDVt2lSxsbH69ttvJf3fDSdr1qypu+66S126dFG9evX03nvvWdt3795dzzzzjMaNG6c2bdro+PHjGjBgQIn7mDlzppKSklS3bl21atXK1jatWrVSq1atlJKSonfeeUetWrVSz549rfXz5s1Tfn6+/vjHPyo0NNR6vPTSSyXuz66bbrpJK1eu1ObNm9WiRQs99thjGjJkiJ5++umrdkygPHOYiz/gBwAAQBHMNAEAANhAaALgNfz9/S/5WLdunafbu6SmTZtesu+33377N+8/PT39ss9Nenp6GZwFgCvh4zkAXuO777675Lqbbrrpircd8JQffvih2FsXSOfvjRQQEPCb9n/u3DkdOHDgkuvDw8NVqRJ3kAGuNkITAACADXw8BwAAYAOhCQAAwAZCEwAAgA2EJgAAABsITQAAADYQmgAAAGwgNAEAANhAaAIAALDh/wMeo2yz0GkQxAAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.countplot(x='mri_count_12_mo', hue='patientsubstatus', data=df_new);"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}