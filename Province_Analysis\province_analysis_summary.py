#!/usr/bin/env python3
"""
Additional Analysis: Detailed Province Discontinuation Patterns
===============================================================

This script provides additional insights into discontinuation patterns,
including demographic analysis and discontinuation reasons by province.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def load_data():
    """Load and prepare the data."""
    df = pd.read_csv("Ocrevus_switch_alerts.csv")
    
    # Create discontinuation status
    df['discontinuation_status'] = df['patientsubstatus'].apply(
        lambda x: 'Discontinued' if 'Discontinued' in str(x) else 'Active'
    )
    
    return df

def analyze_discontinuation_reasons_by_province(df):
    """Analyze specific discontinuation reasons by province."""
    print("\n" + "="*60)
    print("DETAILED DISCONTINUATION REASONS BY HIGH-RISK PROVINCES")
    print("="*60)
    
    # Filter discontinued patients
    discontinued = df[df['discontinuation_status'] == 'Discontinued'].copy()
    
    # Focus on high-risk provinces (BC, NS, QC)
    high_risk_provinces = ['BC', 'NS', 'QC']
    
    for province in high_risk_provinces:
        province_data = discontinued[discontinued['patientprovince'] == province]
        
        if len(province_data) > 0:
            print(f"\n🔍 {province} - Discontinuation Reasons:")
            print(f"   Total discontinued patients: {len(province_data)}")
            
            # Analyze discontinuation reasons
            reasons = province_data['patientsubstatus'].value_counts()
            for reason, count in reasons.head(5).items():
                percentage = (count / len(province_data)) * 100
                print(f"   • {reason}: {count} ({percentage:.1f}%)")

def analyze_demographics_by_province(df):
    """Analyze demographic patterns in discontinuation by province."""
    print("\n" + "="*60)
    print("DEMOGRAPHIC ANALYSIS BY PROVINCE")
    print("="*60)
    
    # Age analysis
    print("\n📊 Average Age by Province and Status:")
    age_analysis = df.groupby(['patientprovince', 'discontinuation_status'])['age'].mean().unstack()
    
    print(f"{'Province':<10} {'Active Avg':<12} {'Discontinued Avg':<18} {'Difference':<12}")
    print("-" * 55)
    
    for province in age_analysis.index:
        active_age = age_analysis.loc[province, 'Active'] if 'Active' in age_analysis.columns else np.nan
        disc_age = age_analysis.loc[province, 'Discontinued'] if 'Discontinued' in age_analysis.columns else np.nan
        
        if not pd.isna(active_age) and not pd.isna(disc_age):
            diff = disc_age - active_age
            print(f"{province:<10} {active_age:<12.1f} {disc_age:<18.1f} {diff:<12.1f}")
    
    # Gender analysis
    print("\n👥 Gender Distribution by Province (Discontinued Patients):")
    gender_disc = df[df['discontinuation_status'] == 'Discontinued'].groupby(['patientprovince', 'gender']).size().unstack(fill_value=0)
    
    if 'F' in gender_disc.columns and 'M' in gender_disc.columns:
        gender_disc['Female_Pct'] = (gender_disc['F'] / (gender_disc['F'] + gender_disc['M'])) * 100
        
        print(f"{'Province':<10} {'Female %':<12} {'Male %':<12}")
        print("-" * 35)
        
        for province in gender_disc.index:
            female_pct = gender_disc.loc[province, 'Female_Pct']
            male_pct = 100 - female_pct
            print(f"{province:<10} {female_pct:<12.1f} {male_pct:<12.1f}")

def analyze_therapy_line_patterns(df):
    """Analyze line of therapy patterns by province."""
    print("\n" + "="*60)
    print("LINE OF THERAPY ANALYSIS")
    print("="*60)
    
    # Calculate discontinuation rates by line of therapy and province
    therapy_analysis = df.groupby(['patientprovince', 'lineoftherapy']).agg({
        'patientid': 'count',
        'discontinuation_status': lambda x: (x == 'Discontinued').sum()
    }).rename(columns={'patientid': 'total', 'discontinuation_status': 'discontinued'})
    
    therapy_analysis['disc_rate'] = (therapy_analysis['discontinued'] / therapy_analysis['total']) * 100
    
    print("\n📈 Discontinuation Rates by Line of Therapy (High-Risk Provinces):")
    high_risk = ['BC', 'NS', 'QC']
    
    for province in high_risk:
        if province in therapy_analysis.index.get_level_values(0):
            print(f"\n{province}:")
            province_data = therapy_analysis.loc[province]
            
            for line, row in province_data.iterrows():
                if row['total'] >= 10:  # Only show lines with sufficient sample size
                    print(f"  Line {line}: {row['disc_rate']:.1f}% ({row['discontinued']}/{row['total']})")

def create_detailed_visualization(df):
    """Create additional visualizations."""
    print("\n" + "="*60)
    print("CREATING DETAILED VISUALIZATIONS")
    print("="*60)
    
    try:
        import matplotlib
        matplotlib.use('Agg')
        
        # Create a comprehensive analysis plot
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. Discontinuation by Age Groups
        df['age_group'] = pd.cut(df['age'], bins=[0, 30, 40, 50, 60, 100], 
                                labels=['<30', '30-39', '40-49', '50-59', '60+'])
        
        age_disc = df.groupby('age_group')['discontinuation_status'].apply(
            lambda x: (x == 'Discontinued').mean() * 100
        )
        
        ax1.bar(age_disc.index, age_disc.values, color='lightcoral', alpha=0.7)
        ax1.set_title('Discontinuation Rate by Age Group', fontweight='bold')
        ax1.set_ylabel('Discontinuation Rate (%)')
        ax1.grid(axis='y', alpha=0.3)
        
        # 2. Discontinuation by Gender and Province (High-risk provinces)
        high_risk_data = df[df['patientprovince'].isin(['BC', 'NS', 'QC'])]
        gender_province = high_risk_data.groupby(['patientprovince', 'gender'])['discontinuation_status'].apply(
            lambda x: (x == 'Discontinued').mean() * 100
        ).unstack()
        
        gender_province.plot(kind='bar', ax=ax2, color=['lightblue', 'lightpink'])
        ax2.set_title('Discontinuation Rate by Gender (High-Risk Provinces)', fontweight='bold')
        ax2.set_ylabel('Discontinuation Rate (%)')
        ax2.legend(['Female', 'Male'])
        ax2.tick_params(axis='x', rotation=0)
        
        # 3. Line of Therapy Distribution
        line_dist = df['lineoftherapy'].value_counts().sort_index()
        ax3.bar(line_dist.index, line_dist.values, color='lightgreen', alpha=0.7)
        ax3.set_title('Patient Distribution by Line of Therapy', fontweight='bold')
        ax3.set_xlabel('Line of Therapy')
        ax3.set_ylabel('Number of Patients')
        ax3.grid(axis='y', alpha=0.3)
        
        # 4. Top Discontinuation Reasons
        disc_reasons = df[df['discontinuation_status'] == 'Discontinued']['patientsubstatus'].value_counts().head(6)
        # Shorten reason names for better display
        short_reasons = [reason.replace('Discontinued-', '').replace('Patient Experienced Adverse Event with Program Drug', 'Adverse Event')[:20] 
                        for reason in disc_reasons.index]
        
        ax4.barh(range(len(short_reasons)), disc_reasons.values, color='orange', alpha=0.7)
        ax4.set_yticks(range(len(short_reasons)))
        ax4.set_yticklabels(short_reasons)
        ax4.set_title('Top Discontinuation Reasons', fontweight='bold')
        ax4.set_xlabel('Number of Patients')
        ax4.grid(axis='x', alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('detailed_province_analysis.png', dpi=200, bbox_inches='tight')
        plt.close()
        
        print("✓ Detailed visualizations saved as 'detailed_province_analysis.png'")
        
    except Exception as e:
        print(f"⚠ Warning: Could not create detailed visualizations: {e}")

def generate_executive_summary(df):
    """Generate executive summary with key findings."""
    print("\n" + "="*60)
    print("EXECUTIVE SUMMARY")
    print("="*60)
    
    total_patients = len(df)
    total_discontinued = len(df[df['discontinuation_status'] == 'Discontinued'])
    overall_rate = (total_discontinued / total_patients) * 100
    
    # Key statistics
    province_stats = df.groupby('patientprovince').agg({
        'patientid': 'count',
        'discontinuation_status': lambda x: (x == 'Discontinued').sum()
    })
    province_stats['rate'] = (province_stats['discontinuation_status'] / province_stats['patientid']) * 100
    
    highest_rate_province = province_stats['rate'].idxmax()
    lowest_rate_province = province_stats['rate'].idxmin()
    
    print(f"📋 KEY FINDINGS:")
    print(f"   • Total patients analyzed: {total_patients:,}")
    print(f"   • Overall discontinuation rate: {overall_rate:.1f}%")
    print(f"   • Highest risk province: {highest_rate_province} ({province_stats.loc[highest_rate_province, 'rate']:.1f}%)")
    print(f"   • Lowest risk province: {lowest_rate_province} ({province_stats.loc[lowest_rate_province, 'rate']:.1f}%)")
    print(f"   • Rate variation: {province_stats['rate'].max() - province_stats['rate'].min():.1f} percentage points")
    
    # High-risk provinces
    high_risk = province_stats[province_stats['rate'] > overall_rate + 5]
    print(f"\n🚨 IMMEDIATE ACTION REQUIRED:")
    print(f"   • {len(high_risk)} provinces require immediate attention")
    print(f"   • Combined patients at risk: {high_risk['patientid'].sum():,}")
    print(f"   • Potential patients to retain: {high_risk['discontinuation_status'].sum():.0f}")
    
    print(f"\n💰 BUSINESS IMPACT:")
    # Assuming average annual treatment cost (estimate)
    avg_annual_cost = 65000  # CAD per patient per year
    potential_revenue_loss = high_risk['discontinuation_status'].sum() * avg_annual_cost
    print(f"   • Estimated annual revenue at risk: ${potential_revenue_loss:,.0f} CAD")
    print(f"   • Average cost per discontinued patient: ${avg_annual_cost:,} CAD")

def main():
    """Main function to run all analyses."""
    print("COMPREHENSIVE OCREVUS DISCONTINUATION ANALYSIS")
    print("=" * 60)
    
    # Load data
    df = load_data()
    
    # Run detailed analyses
    analyze_discontinuation_reasons_by_province(df)
    analyze_demographics_by_province(df)
    analyze_therapy_line_patterns(df)
    create_detailed_visualization(df)
    generate_executive_summary(df)
    
    print("\n" + "="*60)
    print("COMPREHENSIVE ANALYSIS COMPLETE")
    print("="*60)
    print("✓ All analyses completed successfully")
    print("✓ Check 'detailed_province_analysis.png' for additional visualizations")
    print("✓ Review findings above for strategic decision-making")

if __name__ == "__main__":
    main()
