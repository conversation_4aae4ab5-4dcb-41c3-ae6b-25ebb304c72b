#!/usr/bin/env python3
"""
Exploratory Data Analysis: Ocrevus Therapy Discontinuation by Province
======================================================================

This script analyzes discontinuation patterns of Ocrevus therapy across Canadian provinces
to identify provinces with higher discontinuation rates and potential switching patterns.

Author: AI Assistant
Date: 2025
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Set style for better visualizations
plt.style.use('default')
sns.set_palette("husl")

def load_and_validate_data(file_path):
    """Load the CSV data and perform basic validation."""
    print("=" * 60)
    print("OCREVUS THERAPY DISCONTINUATION ANALYSIS BY PROVINCE")
    print("=" * 60)

    try:
        df = pd.read_csv(file_path)
        print(f"✓ Data loaded successfully")
        print(f"✓ Dataset shape: {df.shape[0]:,} rows × {df.shape[1]} columns")
        return df
    except FileNotFoundError:
        print(f"✗ Error: File '{file_path}' not found")
        return None
    except Exception as e:
        print(f"✗ Error loading data: {e}")
        return None

def explore_basic_data_structure(df):
    """Explore basic data structure and key columns."""
    print("\n" + "="*50)
    print("1. BASIC DATA EXPLORATION")
    print("="*50)

    # Display basic info
    print(f"Dataset dimensions: {df.shape}")
    print(f"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")

    # Check key columns
    key_columns = ['patientprovince', 'patientsubstatus', 'discontinue_flag']
    print(f"\nKey columns present: {[col for col in key_columns if col in df.columns]}")

    # Display sample data
    print(f"\nSample data (first 3 rows):")
    print(df[['patientid', 'patientprovince', 'patientsubstatus', 'age', 'gender']].head(3))

    return df

def validate_and_clean_data(df):
    """Validate and clean the data for analysis."""
    print("\n" + "="*50)
    print("2. DATA VALIDATION & CLEANING")
    print("="*50)

    # Check for missing values in key columns
    key_cols = ['patientprovince', 'patientsubstatus']
    missing_data = df[key_cols].isnull().sum()
    print("Missing values in key columns:")
    for col, missing in missing_data.items():
        print(f"  {col}: {missing:,} ({missing/len(df)*100:.1f}%)")

    # Remove rows with missing province data
    initial_count = len(df)
    df_clean = df.dropna(subset=['patientprovince', 'patientsubstatus']).copy()
    removed_count = initial_count - len(df_clean)

    if removed_count > 0:
        print(f"\n✓ Removed {removed_count:,} rows with missing key data")
        print(f"✓ Clean dataset: {len(df_clean):,} rows")

    # Create discontinuation flag if not present
    if 'discontinue_flag' not in df_clean.columns:
        df_clean['discontinuation_status'] = df_clean['patientsubstatus'].apply(
            lambda x: 'Discontinued' if 'Discontinued' in str(x) else 'Active'
        )
    else:
        df_clean['discontinuation_status'] = df_clean['discontinue_flag'].apply(
            lambda x: 'Discontinued' if x == 1 else 'Active'
        )

    print(f"\n✓ Created discontinuation status variable")
    print("Discontinuation status distribution:")
    status_counts = df_clean['discontinuation_status'].value_counts()
    for status, count in status_counts.items():
        print(f"  {status}: {count:,} ({count/len(df_clean)*100:.1f}%)")

    return df_clean

def analyze_province_discontinuation_rates(df):
    """Analyze discontinuation rates by province."""
    print("\n" + "="*50)
    print("3. PROVINCE-WISE DISCONTINUATION ANALYSIS")
    print("="*50)

    # Calculate discontinuation rates by province
    province_analysis = df.groupby('patientprovince').agg({
        'patientid': 'count',
        'discontinuation_status': lambda x: (x == 'Discontinued').sum()
    }).rename(columns={
        'patientid': 'total_patients',
        'discontinuation_status': 'discontinued_patients'
    })

    # Calculate discontinuation rate
    province_analysis['discontinuation_rate'] = (
        province_analysis['discontinued_patients'] / province_analysis['total_patients'] * 100
    )

    # Sort by discontinuation rate (descending)
    province_analysis = province_analysis.sort_values('discontinuation_rate', ascending=False)

    # Calculate overall discontinuation rate
    overall_rate = (df['discontinuation_status'] == 'Discontinued').mean() * 100

    print(f"Overall discontinuation rate: {overall_rate:.1f}%")
    print(f"\nProvince-wise discontinuation rates:")
    print("-" * 70)
    print(f"{'Province':<10} {'Total':<8} {'Discontinued':<12} {'Rate (%)':<10} {'vs Avg':<10}")
    print("-" * 70)

    for province, row in province_analysis.iterrows():
        rate_diff = row['discontinuation_rate'] - overall_rate
        diff_indicator = "↑" if rate_diff > 0 else "↓" if rate_diff < 0 else "="
        print(f"{province:<10} {row['total_patients']:<8} {row['discontinued_patients']:<12} "
              f"{row['discontinuation_rate']:<10.1f} {diff_indicator}{abs(rate_diff):.1f}")

    return province_analysis, overall_rate

def create_visualizations(df, province_analysis, overall_rate):
    """Create visualizations for discontinuation analysis."""
    print("\n" + "="*50)
    print("4. CREATING VISUALIZATIONS")
    print("="*50)

    try:
        # Set matplotlib backend to avoid display issues
        import matplotlib
        matplotlib.use('Agg')  # Use non-interactive backend

        # Set up the plotting area
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # Visualization 1: Discontinuation rates by province (bar chart)
        provinces = province_analysis.index
        rates = province_analysis['discontinuation_rate']
        colors = ['#d62728' if rate > overall_rate else '#1f77b4' for rate in rates]

        bars = ax1.bar(provinces, rates, color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)
        ax1.axhline(y=overall_rate, color='orange', linestyle='--', linewidth=2,
                    label=f'National Average ({overall_rate:.1f}%)')
        ax1.set_title('Ocrevus Discontinuation Rates by Province', fontsize=12, fontweight='bold')
        ax1.set_xlabel('Province', fontsize=10)
        ax1.set_ylabel('Discontinuation Rate (%)', fontsize=10)
        ax1.legend()
        ax1.grid(axis='y', alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)

        # Add value labels on bars
        for bar, rate in zip(bars, rates):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{rate:.1f}%', ha='center', va='bottom', fontsize=8)

        # Visualization 2: Patient volume vs discontinuation rate (scatter plot)
        ax2.scatter(province_analysis['total_patients'], province_analysis['discontinuation_rate'],
                    s=80, alpha=0.7, c=colors, edgecolors='black')

        # Add province labels
        for province, row in province_analysis.iterrows():
            ax2.annotate(province, (row['total_patients'], row['discontinuation_rate']),
                        xytext=(3, 3), textcoords='offset points', fontsize=8)

        ax2.axhline(y=overall_rate, color='orange', linestyle='--', linewidth=2, alpha=0.7)
        ax2.set_title('Patient Volume vs Discontinuation Rate', fontsize=12, fontweight='bold')
        ax2.set_xlabel('Total Patients', fontsize=10)
        ax2.set_ylabel('Discontinuation Rate (%)', fontsize=10)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('province_discontinuation_analysis.png', dpi=200, bbox_inches='tight')
        plt.close()  # Close the figure to free memory

        print("✓ Visualizations created and saved as 'province_discontinuation_analysis.png'")

    except Exception as e:
        print(f"⚠ Warning: Could not create visualizations: {e}")
        print("✓ Analysis completed successfully without visualizations")

def identify_outliers_and_insights(province_analysis, overall_rate):
    """Identify provinces with significantly different discontinuation rates."""
    print("\n" + "="*50)
    print("5. KEY INSIGHTS & RECOMMENDATIONS")
    print("="*50)

    # Define thresholds for outliers
    high_threshold = overall_rate + 5  # 5% above average
    low_threshold = overall_rate - 5   # 5% below average

    high_discontinuation = province_analysis[
        province_analysis['discontinuation_rate'] > high_threshold
    ]
    low_discontinuation = province_analysis[
        province_analysis['discontinuation_rate'] < low_threshold
    ]

    print("🔴 HIGH-RISK PROVINCES (>5% above national average):")
    if len(high_discontinuation) > 0:
        for province, row in high_discontinuation.iterrows():
            print(f"  • {province}: {row['discontinuation_rate']:.1f}% "
                  f"({row['discontinued_patients']}/{row['total_patients']} patients)")
    else:
        print("  • No provinces significantly above average")

    print(f"\n🟢 LOW-RISK PROVINCES (>5% below national average):")
    if len(low_discontinuation) > 0:
        for province, row in low_discontinuation.iterrows():
            print(f"  • {province}: {row['discontinuation_rate']:.1f}% "
                  f"({row['discontinued_patients']}/{row['total_patients']} patients)")
    else:
        print("  • No provinces significantly below average")

    # Additional insights
    print(f"\n📊 STATISTICAL SUMMARY:")
    print(f"  • Highest discontinuation rate: {province_analysis['discontinuation_rate'].max():.1f}% "
          f"({province_analysis['discontinuation_rate'].idxmax()})")
    print(f"  • Lowest discontinuation rate: {province_analysis['discontinuation_rate'].min():.1f}% "
          f"({province_analysis['discontinuation_rate'].idxmin()})")
    print(f"  • Rate spread: {province_analysis['discontinuation_rate'].max() - province_analysis['discontinuation_rate'].min():.1f} percentage points")

    # Recommendations
    print(f"\n💡 ACTIONABLE RECOMMENDATIONS:")
    if len(high_discontinuation) > 0:
        print(f"  1. Investigate high discontinuation rates in: {', '.join(high_discontinuation.index)}")
        print(f"  2. Conduct targeted interventions in high-risk provinces")
        print(f"  3. Analyze specific discontinuation reasons in these provinces")

    if len(low_discontinuation) > 0:
        print(f"  4. Study best practices from low-discontinuation provinces: {', '.join(low_discontinuation.index)}")
        print(f"  5. Consider replicating successful strategies from these provinces")

    print(f"  6. Monitor provinces with small patient populations for statistical significance")

    return high_discontinuation, low_discontinuation

def main():
    """Main analysis function."""
    # File path
    file_path = "Ocrevus_switch_alerts.csv"

    # Load and validate data
    df = load_and_validate_data(file_path)
    if df is None:
        return

    # Explore basic structure
    df = explore_basic_data_structure(df)

    # Clean and validate data
    df_clean = validate_and_clean_data(df)

    # Analyze discontinuation rates by province
    province_analysis, overall_rate = analyze_province_discontinuation_rates(df_clean)

    # Create visualizations
    create_visualizations(df_clean, province_analysis, overall_rate)

    # Identify insights and recommendations
    high_risk, low_risk = identify_outliers_and_insights(province_analysis, overall_rate)

    print("\n" + "="*60)
    print("ANALYSIS COMPLETE")
    print("="*60)
    print("✓ Province discontinuation analysis completed successfully")
    print("✓ Results saved to 'province_discontinuation_analysis.png'")
    print("✓ Review the insights above for actionable recommendations")

if __name__ == "__main__":
    main()
